import datetime
import json
import os
import csv
import pytz
import requests
import pandas as pd
import numpy as np
import re
from celery.utils.log import get_task_logger
from celery import (shared_task, )
from django_celery_results.models import TaskResult
from django.core.mail import send_mail
from django.db.models import Q
from .models import (Course, LMSBranch, UserSettings, CourseLicenseUser, CourseLicense, UserReport, GCIndexAssessment,
                     GCIndexReport, CourseUserAssignment, ElearninStates, ElearningProject, User, Event, SurveyResponse, AISurveyResponse)
from requests.auth import HTTPBasicAuth
from .utils import bulk_upsert
from django.http import HttpResponse
from rest_framework.authtoken.models import Token
from .serializers import CourseSerializer, LMSBranchSerializer
from pytz import timezone
from datetime import datetime, timedelta, date
from deviare import settings as deviare_settings
india_tz = pytz.timezone('Asia/Kolkata')

from .utils import add_user_to_course, usersignup
from clint.textui import progress
from .utils import get_link
from django.db import transaction
from django.utils import timezone
import pdb
from django.core.exceptions import ObjectDoesNotExist
import base64
from dateutil import parser

# Manual Triggered tasks
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
from django_celery_results.models import TaskResult
from celery_tasks_api.models import TriggeredTask

logger = get_task_logger(__name__)
API_KEY = deviare_settings.TLMS_API_KEY
URL_ADDRESS = f"{deviare_settings.TLMS_URL_ADDRESS}/api/v1"
#URL_ADDRESS = "https://learning.staging.deviare.co.za/api/v1"

GCINDEX_API_KEY = 'e837722869ab72e624667648e5da7f5c'
GCINDEX_API_URL = "https://gcindex.eg1.co.uk/api.php"
url = f"{GCINDEX_API_URL}?key={GCINDEX_API_KEY}"
# Define environment config using environment variables
REVINOVA_ENV_CONFIG = {
    'prod': {
        'client_id': deviare_settings.PROD_CLIENT_ID,
        'client_secret': deviare_settings.PROD_CLIENT_SECRET,
        'xAPIKey': deviare_settings.PROD_X_API_KEY,
        'ExternalAPIURL': deviare_settings.PROD_EXTERNAL_API_URL,
    }
}


#################################################### Custome Fuctions #################################################


# Create or get token
def create_or_get_token(user):
    token = Token.objects.get_or_create(user=user)
    for i in token:
        return i.key


# get talentLMS API key
def lms_apiKey():
    return "ck_3f5643c456986d08c1936d93620966da1a43a073"


# get talentLMS headers
def get_lmsHeader():
    headers = {
        'Authorization': 'Basic aUNTWVQ2NUtFQlhQS0lHajRxdHJTSnl4M1k1Y2xNOg==',
        'Cookie': 'AWSALB=3NwoxAMJPAAIiwKyRp+8zT2ls6tyNJoNhv4v2X155WWIQ0eBIzUCwJL5rKoaOMpcOrOz78eDmJp7SXF6Wi4+B/ZD9Kt6nfivPH+jkf0YSFveUXldpxzqqUTYgGws; AWSALBCORS=3NwoxAMJPAAIiwKyRp+8zT2ls6tyNJoNhv4v2X155WWIQ0eBIzUCwJL5rKoaOMpcOrOz78eDmJp7SXF6Wi4+B/ZD9Kt6nfivPH+jkf0YSFveUXldpxzqqUTYgGws; PHPSESSID=elb~d9ssspk3bum4fcsbpr521hqmlv'
    }
    return headers


# check for existing branch and but right now no need this talent lms take care of this
def find_lmsbranch(branch, branch_id=None):
    if branch_id:
        pass
        # url = URL_ADDRESS+'/branches'
    else:
        url = URL_ADDRESS + '/branches'
        response = requests.request("GET", url, headers=get_lmsHeader(), auth=(API_KEY, ''))
        try:
            available_branch = [i for i in eval(response.text.replace("null", "12345")) if
                                'name' in i and i['name'] == branch]
        except:
            available_branch = []
        if available_branch == []:
            return False
        else:
            return True


# create branch on talentLMS during user creation
def createbranch_Onlms(branch, api_key, link):
    if branch and link:
        # url = link + '/api/v1/createbranch'
        url = "https://deviare.talentlms.com/api/v1/createbranch"
        payload = {"name": branch, "description": "", "disallow_global_login": "", "group_id": "", "language": ""}
        if url == URL_ADDRESS + '/createbranch':
            response = requests.request("POST", url, headers=get_lmsHeader(), auth=(API_KEY, ''), data=payload)
        else:
            response = requests.request("POST", url, headers=get_lmsHeader(), auth=HTTPBasicAuth(api_key, ''),
                                        data=payload)
        return {"message": "branch created sucessfully", "data": response.text, "status": True}
    else:
        return {"message": "branch does not create", "data": {}, "status": True}


def update_branch(branch):
    url = f"{deviare_settings.TLMS_URL_ADDRESS}/api/v1/branches"
    response = requests.request("GET", url, headers=get_lmsHeader(), auth=(API_KEY, ''))
    if response.status_code == 200:
        branch_id = [i['id'] for i in eval(response.text.replace("null", "1234")) if i['name'] == branch]
        if branch_id != []:
            return branch_id[0]
    else:
        return None


def createCourseOnLMS(course):
    if course:
        get_url = URL_ADDRESS + "/courses/"
        response = requests.request("GET", get_url, headers=get_lmsHeader(), auth=(API_KEY, ''))
        try:
            available_course = [i for i in eval(response.text.replace("null", "12345")) if
                                'name' in i and i['name'] == course]
        except:
            available_course = []
        if available_course != []:
            return {"message": "course already exist", "data": available_course[0], "status": True}
        url = API_KEY + '/createcourse'
        payload = {"name": course, "description": "", "code": "", "price": "", "time_limit": "", "category_id": "",
                   "creator_id": "", "custom_field_1": ""}
        response = requests.request("POST", url, headers=get_lmsHeader(), auth=(API_KEY, ''), data=payload)
        return {"message": "course created sucessfully", "data": response.text, "status": True}
    else:
        return {"message": "course does not create", "data": {}, "status": True}


# Create branch for LMSBranch model, we changed model hierarchy so for create branch differnt from company model run this function
def all_user_branches(request):
    users = UserSettings.objects.all()
    for i in users:
        if i.companyID != None and i.companyID.branch != "":
            token = create_or_get_token(i.user)
            branch = creatBranchOnModel(i.companyID.branch, token)
    return HttpResponse({"message": "branches created sucessfully"})


# This is child fuction of previous function
def creatBranchOnModel(branch, token, link, timezone, branch_id):
    try:
        exist_brnach = LMSBranch.objects.get(lmsbranch_id=branch_id)
    except:
        exist_brnach = None
    if exist_brnach:
        exist_brnach.name = branch
        exist_brnach.timezone = timezone
        exist_brnach.save()
        response = {}
        response['uuid'] = exist_brnach.uuid
        response['name'] = exist_brnach.name
        response['lmsbranch_id'] = exist_brnach.lmsbranch_id
        response['timezone'] = exist_brnach.timezone
        response['link'] = exist_brnach.link
        return {"data": response, "message": "branch updated sucessfully ", "status": True}

    branch_Data = {"name": branch, 'lmsbranch_id': branch_id, 'timezone': timezone, 'link': link}
    serializer = LMSBranchSerializer(data=branch_Data)
    if serializer.is_valid(raise_exception=True):
        serializer.save()
        return {"data": serializer.data, "message": "branch created sucessfully", "status": True}
    return {"data": serializer.data, "message": "branch does not create ", "status": False}


# add courses to branch on talentLMS
# Use this for cron Job
def addCourse_tobranch(course_id, branch_id):
    course = None
    branch_name = None
    courses = CourseLicense.objects.all()
    for i in courses:
        try:
            course = i.course_id.name
        except:
            course = None
        try:
            branch_name = i.project_id.company_id.branch_id.name
        except:
            branch_name = None
        if course != None and branch_name != None:
            branch_lms = createbranch_Onlms(branch_name, '', deviare_settings.TLMS_URL_ADDRESS)
            if ('data' in branch_lms and 'error' in branch_lms['data']) and (
                    'message' in eval(branch_lms['data'])['error']) and (
                    eval(branch_lms['data'])['error']['message'] == "A branch with the same name already exists"):
                get_url = URL_ADDRESS + "/branches"
                response = requests.request("GET", get_url, headers=get_lmsHeader(), auth=(API_KEY, ''))
                try:
                    available_branch = [i for i in eval(response.text.replace("null", "12345")) if
                                        'name' in i and i['name'] == branch_name]
                except:
                    available_branch = []
                if available_branch != []:
                    branch_id = available_branch[0]['id']
                    course = createCourseOnLMS(course)
                    if ('data' in course) and ('message' in course) and (course['message'] == "course already exist"):
                        course_id = course['data']['id']
                        usertocourse = add_courseTobranch(course_id, branch_id)
                    elif ('message' in course and course['message'] == 'course created sucessfully') and (
                            'data' in course and 'id' in eval(course['data'].replace("null", "12345"))):
                        course_id = eval(course['data'].replace("null", "12345"))['id']
                        add_user = add_courseTobranch(course_id, branch_id)

            elif ('message' in branch_lms and branch_lms['message'] == 'branch created sucessfully') and (
                    'data' in branch_lms and 'id' in eval(branch_lms['data'].replace("null", "12345"))):
                branch_id = eval(branch_lms['data'].replace("null", "12345"))['id']
                course = createCourseOnLMS(course)
                if ('data' in course) and ('message' in course) and (course['message'] == "course already exist"):
                    course_id = course['data']['id']
                    usertocourse = add_courseTobranch(course_id, branch_id)
                elif ('message' in course and course['message'] == 'course created sucessfully') and (
                        'data' in course and 'id' in eval(course['data'].replace("null", "12345"))):
                    course_id = eval(course['data'].replace("null", "12345"))['id']
                    add_user = add_courseTobranch(course_id, branch_id)
    return HttpResponse({"message": "user added to branch"})


# add cuser to course on talentLMS
# Use this for cron Job
def addUser_tocourse(request):
    courses = CourseUserAssignment.objects.all()
    for i in courses:
        try:
            course = i.course
        except:
            course = None
        try:
            user = i.user
        except:
            user = None
        if course != None and user != None and user.user_id_talentlms != None:
            course_name = course.name
            talent_id = user.user_id_talentlms
            create_course = createCourseOnLMS(course_name)
            if ('data' in create_course) and ('message' in create_course) and (
                    create_course['message'] == "course already exist"):
                course_id = create_course['data']['id']
                add_user = add_userTocourse(str(course_id), str(talent_id))
            elif ('message' in create_course and create_course['message'] == 'course created sucessfully') and (
                    'data' in create_course and 'id' in eval(create_course['data'].replace("null", "12345"))):
                course_id = eval(create_course['data'].replace("null", "12345"))['id']
                add_user = add_userTocourse(str(course_id), str(talent_id))
    return HttpResponse({"message": "course added to user"})


# For add user to branch on taletLMS
# user this for cron job
# @api_view(['GET'])
def allocate_userTobranchlms(user_id, branch_id):
    users = UserSettings.objects.filter(uuid='<EMAIL>')
    for i in users:
        if i.user_id_talentlms != None and i.customers != None and i.customers.first().branch_id.lmsbranch_id != None:
            # create_user = add_user_to_talent_lms()
            # branch_name = i.companyID.branch_id.name
            talent_id = i.user_id_talentlms
            # branch_lms = createbranch_Onlms(branch_name,'',"https://deviare.talentlms.com")
            branch_id = i.customers.first().branch_id.lmsbranch_id
            add_user = add_userTobranch(str(branch_id), talent_id)
            # if ('data' in branch_lms and 'error' in branch_lms['data']) and ('message' in eval(branch_lms['data'])['error']) and (eval(branch_lms['data'])['error']['message'] == "A branch with the same name already exists"):
            #     get_url = URL_ADDRESS+"/branches"
            #     response = requests.request("GET", get_url, headers=get_lmsHeader(),auth=(API_KEY,''))
            #     try:
            #        available_branch = [i for i in eval(response.text.replace("null","12345")) if 'name' in i and i['name'] == branch_name ]
            #     except:
            #        available_branch = []
            #     if available_branch != []:
            #         add_user = add_userTobranch(available_branch[0]['id'],talent_id )
            # elif ('message' in branch_lms and branch_lms['message'] == 'branch created sucessfully') and ('data' in branch_lms and 'id' in eval(branch_lms['data'].replace("null","12345"))):
            #     branch_id = eval(branch_lms['data'].replace("null","12345"))['id']
            #     add_user = add_userTobranch(str(branch_id),talent_id )
    return HttpResponse({"message": "user added to branch"})


# Child fuctions of prevoius fuction
def add_userTobranch(branch_id, user_id):
    url = URL_ADDRESS + "/addusertobranch/user_id:" + user_id + ",branch_id:" + branch_id
    response = requests.request("GET", url, headers=get_lmsHeader(), auth=HTTPBasicAuth(API_KEY, ''))
    return {"message": "branch created on talentLMS", "data": response.text}


def add_courseTobranch(course_id, branch_id):
    try:
        url = URL_ADDRESS + "/addcoursetobranch/course_id:" + str(course_id) + ",branch_id:" + str(branch_id)
        response = requests.request("GET", url, headers=get_lmsHeader(), auth=HTTPBasicAuth(API_KEY, ''))
        return {"message": "branch created on talentLMS", "data": response.text}
    except:
        return None


def add_userTocourse(course_id, talant_id):
    url = URL_ADDRESS + "/addusertocourse"
    payload = {"user_id": talant_id, "course_id": course_id, "role": 'learner'}
    response = requests.request("POST", url, headers=get_lmsHeader(), auth=HTTPBasicAuth(API_KEY, ''), data=payload)
    return {"message": "branch created on talentLMS", "data": response.text}

def extract_revinova_learning_object_data(json_data):
    extracted_data_list = []
    try:
        for item in json_data['data']:
            # Check if the item has a category with title "Language" and tag title "English"
            #print(f"item is --- {item}")
            # Ignore the record if category is null
            if item['category'] is None:
                continue
            has_english_language = any(
                category['title'] == 'Language' and any(tag['title'] == 'English' for tag in category['tags'])
                for category in item['category']
            )
            if has_english_language:
                extracted_data = {
                    'learningobject_id': item['learningobject_id'],
                    'learningobject_type': item['learningobject_type'],
                    'display_name': item['display_name'],
                    'launch_url': item['launch_url'],
                    'created_date': item['created_date'],
                    'updated_date': item['updated_date'],
                    'short_description': item['short_description'],
                    'duration': item['duration'],
                    'category': {category['title']: tag['title'] for category in item['category'] for tag in category['tags']}
                }
                extracted_data_list.append(extracted_data)
    except KeyError as e:
        print(f"Error: Key {e} not found in JSON data")
        return None 
    return extracted_data_list

@shared_task(name="get_all_courses_from_revinova", bind=True)
def get_all_courses_from_revinova(*args, **kwargs):

    try:
        url = 'https://api.raven360.com/gettoken'
        headers = {
            'x-api-key': 'Dk9fQDLKkW76lSn4YjTdH1m7FtgLwh0D3Oh2qXSG',
            'Content-Type': 'application/json'
        }
        data = {
            'client_id': '6W1I82srzyle21KA3ISYt7zha',
            'client_secret': 'QB0coCnuEYtVcwq5rr0iecp4fjHhpPdNQd70F5hYpIdPFae00a'
        }
        response = requests.post(url, headers=headers, json=data)
        token_response = response.json()
        token = token_response['data']['token']

    except Exception as e:
        print(f"Error: {e}")
        response = None
    # get learning object
    try:
        url = 'https://api.raven360.com/administration/catalog/learningobjects'
        headers = {
            'x-api-key': 'Dk9fQDLKkW76lSn4YjTdH1m7FtgLwh0D3Oh2qXSG',
            'Authorization': token,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        data = {
            'learningobject_type': 'Content',
            'from_date': '01-01-2022',
            'to_date': date.today().strftime('%m-%d-%Y'),
            "status": "0"
        }
        response = requests.post(url, headers=headers, json=data)
        json_data = response.json()
    except Exception as e:
        print(f"Error: {e}")
        json_data = None
    # update or create the course - aws revinova
    if json_data:
        revinova_course = extract_revinova_learning_object_data(json_data)
    else:
        print("Error: No data to process")



    if revinova_course:
        for course in revinova_course:
            try:
                c = {'name': course['display_name'],
                     'description': course['short_description'],
                     'provider': 'Revinova',
                     'link': course['launch_url'],
                     'category': course['category']['Domain'],
                     'course_id': course['learningobject_id'],
                     'course_type': 'Learning'}
                obj, created = Course.objects.update_or_create(
                            course_id=course['learningobject_id'],
                            course_type='Learning', defaults=c)
                #pdb.set_trace()  # Pause execution here for testing

            except Exception as e:
                print(f"Error: {e}")

# tasks to add the sync progress to the DB (revinova_sync_progress_to_db)
@shared_task(name="get_learning_objects_progress", bind=True)
def get_learning_objects_progress(self, environment='prod'):
    config = REVINOVA_ENV_CONFIG[environment]
    
    # Step 1: Get the token
    token_url = f"{config['ExternalAPIURL']}/gettoken"
    headers = {
        'x-api-key': config['xAPIKey'],
        'Content-Type': 'application/json'
    }
    data = {
        'client_id': config['client_id'],
        'client_secret': config['client_secret']
    }
    try:
        response = requests.post(token_url, headers=headers, json=data)
        response.raise_for_status()
        token_response = response.json()
        token = token_response['data']['token']
    except Exception as e:
        print(f"Error getting token: {e}")
        return None

    # Step 2: Use the token to make the second API request
    api_url = f"{config['ExternalAPIURL']}/administration/progress/learningobjects"
    headers = {
        'x-api-key': config['xAPIKey'],
        'Authorization': token,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    data = {
        'learningobject_type': 'Content',
        'from_date': '01-01-2022',
        'to_date': date.today().strftime('%m-%d-%Y')
    }
    try:
        response = requests.post(api_url, headers=headers, json=data)
        response.raise_for_status()
        json_data = response.json()

        # Step 3: Process the 'data' list from the response
        if 'data' in json_data:
            process_learning_object_data_list(json_data['data'])
        else:
            print("No 'data' key in response:", json_data)
        # return json_data
    except Exception as e:
        print(f"Error getting learning objects: {e}")
        return None
    
# function to insert revoniva progress to the DB
# def process_learning_object_data_list(data_list):
#     for data in data_list:
#         # Extract required fields from the sample data
#         user_id = data['user_id']
#         email_id = data['email_id']
#         learningobject_id = data['learningobject_id']
#         completion_percentage = data['completion_percentage']

#         # Fetch additional data from the main_course and user_settings tables
#         try:
#             course = Course.objects.get(id=learningobject_id)
#             course_title = course.title
#         except Exception as e:
#             print(f"Course with ID {learningobject_id} does not exist. - {e}")
#             continue

#         try:
#             user_settings = UserSettings.objects.get(email=email_id)
#             final_users_new_id = user_settings.user_id
#         except UserSettings.DoesNotExist:
#             print(f"User with email {email_id} does not exist.")
#             continue

#         # Insert the mapped data into the main_elearninstates table
#         ElearninStates.objects.create(
#             course_id=learningobject_id,
#             course_title=course_title,
#             self_learning_completion=completion_percentage,
#             user_id=user_id,
#             is_xpertskill_course=False,  # Assuming a default value, update as needed
#             final_users_new_id=final_users_new_id,
#             email=email_id
#         )

#         print("Data inserted successfully.")

def process_learning_object_data_list(data_list):
    # Pre-fetch data
    course_ids = [str(data['learningobject_id']) for data in data_list]
    email_ids = [data['email_id'] for data in data_list]
    courses = {str(c.course_id): c for c in Course.objects.filter(course_id__in=course_ids)}
    users = {u.email: u for u in UserSettings.objects.filter(email__in=email_ids)}

    for data in data_list:
        email_id = data['email_id']
        learningobject_id = str(data['learningobject_id'])
        completion_percentage = data.get('completion_percentage', 0)
        last_access_date = data.get('last_access_date')
        completed_date = data.get('completed_date')
        first_access_date = data.get('first_access_date')

        # Custom logic for new fields
        certification_status = "Certified" if completion_percentage == 100 else "Not Certified"
        project_result = "passed" if completion_percentage == 100 else "pending"
        course_type = "Online Classroom"
        course_assignment_date = first_access_date
        course_activation_date = first_access_date
        course_completion_date = completed_date
        last_login_date = last_access_date
        last_activity_on = completed_date

        # Check if course and user exist
        course = courses.get(learningobject_id)
        if not course:
            print(f"Course with course_id {learningobject_id} does not exist.")
            continue

        user_settings = users.get(email_id)
        if not user_settings:
            print(f"User with email {email_id} does not exist.")
            continue

        # Optimization: Skip if already completed and new data is also completed
        try:
            existing = ElearninStates.objects.get(email=email_id, course_id=learningobject_id)
            if existing.self_learning_completion == 100 and completion_percentage == 100:
                print(f"Skipping user {email_id} for course {learningobject_id} (already completed).")
                continue
        except ElearninStates.DoesNotExist:
            pass

        # Insert or update ElearninStates
        try:
            obj, created = ElearninStates.objects.update_or_create(
                email=email_id,
                course_id=learningobject_id,
                defaults={
                    'course_title': course.name,
                    'self_learning_completion': completion_percentage,
                    'user': user_settings,  # ForeignKey to UserSettings
                    'final_users_new': user_settings.user,  # ForeignKey to User
                    'is_xpertskill_course': False,
                    'certification_status': certification_status,
                    'project_result': project_result,
                    'last_login_date': last_login_date,
                    'last_activity_on': last_activity_on,
                    'course_type': course_type,
                    'course_assignment_date': course_assignment_date,
                    'course_activation_date': course_activation_date,
                    'course_completion_date': course_completion_date,
                }
            )
            action = "inserted" if created else "updated"
            print(f"ElearninStates {action} successfully for user {user_settings.reset_id}.")
        except Exception as e:
            print(f"Error processing ElearninStates for user {email_id}: {e}")

        # Insert or update CourseLicenseUser for dashboard counts
        try:
            # Find existing CourseLicenseUser entries for this course and user
            existing_clus = CourseLicenseUser.objects.filter(
                course_license_id__course_id=course,
                user_id=user_settings
            )
            
            if existing_clus.exists():
                # Update all matching entries
                update_count = existing_clus.update(course_completion=float(completion_percentage))
                print(f"Updated course_completion to {completion_percentage}% for {update_count} CourseLicenseUser records (user: {user_settings.reset_id}, course: {course.name})")
            else:
                # Skip if no entry exists
                print(f"No CourseLicenseUser record found for user {user_settings.reset_id} and course {course.name}. Skipping update.")
        except Exception as e:
            print(f"Error updating CourseLicenseUser for {email_id}: {e}")



@shared_task(name="add_userToLMSwithBranch", bind=True)
def add_userToLMSwithBranch(username_arr, a=None):
    try:
        for i in username_arr:
            user = UserSettings.objects.filter(userName=i).first()
            url = "%s/usersignup" % URL_ADDRESS
            payload = {
                "first_name": user.firstName,
                "last_name": user.lastName,
                # "email": 'dnedit__'+kwargs['kwargs']['email'],
                "email": user.email,
                "login": user.userName,
                "password": "icecream1781"
            }
            files = []
            headers = {}

            response = requests.request(
                "POST",
                url,
                auth=HTTPBasicAuth(API_KEY, ""),
                headers=headers,
                data=payload,
                files=files,
            )

            if response.status_code == 200:
                # User successfully created on TalentLMS
                content = json.loads(response.text)
                user = UserSettings.objects.get(email=user.email)
                user.user_id_talentlms = content["id"]
                user.save()
                user_id = content["id"]
                branch_id = user.customers.all().first().branch_id.lmsbranch_id
                url = URL_ADDRESS + "/addusertobranch/user_id:" + str(user_id) + ",branch_id:" + str(branch_id)
                response = requests.request("GET", url, headers=get_lmsHeader(), auth=HTTPBasicAuth(API_KEY, ''))
                return {"message": "branch created on talentLMS", "data": response.text}

            else:
                logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


def gcapi(**kw):
    payload = {
        "action": "fetch"
    }
    payload.update({k: v for k, v in kw.items() if k in ['action', 'token']})
    headers = {
        'Content-Type': 'application/json'
    }
    query = requests.get
    if payload.get('action') in ['create', 'delete']:
        query = requests.post

    return query(url, headers=headers, data=json.dumps(payload))


@shared_task(name="create_course_on_talent_lms", bind=True)
def create_course_on_talent_lms(self, pk=None):
    """
    Post course content to Talent LMS
    """
    try:
        url = "%s/createcourse" % URL_ADDRESS

        course = Course.objects.get(pk=pk)
        serializer = CourseSerializer(course)

        content = serializer.data
        # Field values required to create a course on talentLMS
        course_creation_keys = {
            "name",
            "description",
            "code",
            "price",
            "time_limit",
            "category_id",
            "creator_id",
        }

        payload = {c: content[c] for c in content.keys() & course_creation_keys}

        files = []
        headers = {}

        response = requests.request(
            "POST",
            url,
            auth=HTTPBasicAuth(API_KEY, ""),
            headers=headers,
            data=payload,
            files=files,
        )

        logger.info(response.text.encode("utf8"))

    except Exception as exc:
        logger.exception(exc)


@shared_task(bind=True, name="get_all_courses_from_talent_lms")
def get_all_courses_from_talent_lms(self):
    """
    Periodic task. Retrieve all course content from
    talent lms platform
    """
    try:

        url = "%s/courses" % URL_ADDRESS
        payload = {}
        headers = {}

        response = requests.request("GET", url, auth=(API_KEY, ""), headers=headers, data=payload)

        # List of registered courses on talent lms
        if response.status_code == 200:
            content = json.loads(response.text)
            df = pd.DataFrame(content)
            if not df.empty:
                # cols = list(set(df.columns.tolist()).intersection({c.name for c in Course._meta.get_fields()}))
                # print(cols)
                # cols.append('id')
                # cols.append('custom_field_1')

                # content = df[cols].to_dict(orient='records')
                cats = {}
                response = requests.request("GET", "%s/categories" % URL_ADDRESS, auth=(API_KEY, ""), headers=headers,
                                            data=payload)
                if response.status_code == 200:
                    categories = json.loads(response.text)
                    for category in categories:
                        cats[category["id"]] = category["name"]
                bulk_upsert(content, cats)

        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)
        self.retry(exc=exc)


@shared_task(bind=True,name='get_all_courses_from_xpert_skills')
def get_all_courses_from_xpert_skills(self, *args, **kwargs):
    """
    Periodic task. Retrieve all course content from
    talent lms platform
    """
    try:
        URL_ADDRESS = "https://www.learning.deviareacademy.africa/cf_api/v1/productslist?partner_id=47"
        url = URL_ADDRESS + "&oauth_consumer_key=At3KQZwqU8z43HrcK5cuCC4PgHxnjPYp&oauth_signature_method=HMAC-SHA1" \
                            "&oauth_nonce=mQk6mlRKl1y&oauth_version=1.0" \
                            "&oauth_signature=mZoSytmAhImAs9EDwGdL0psIZqQ%3D"
        payload = {}
        headers = {}

        response = requests.get(url, headers=headers, data=payload)
        # List of registered courses on xpert skills
        if response.status_code == 200:
            content = json.loads(response.text)
            df = pd.DataFrame(content)
            if not df.empty:
                for course in content:
                    if course['type'] == 'course':
                        course["course_id_talent_lms"] = course.pop('product_id')
                        course["certification_duration"] = course.pop('course_duration')
                        course["provider"] = "Xpert Skills"
                        course['name'] = course.pop('title')
                        course.pop('exam_questions')
                        course.pop('creation_date')
                        course.pop('type')
                        obj, created = Course.objects.update_or_create(
                            course_id_talent_lms=course["course_id_talent_lms"],
                            e_commerce=False, defaults=course)
                    elif course['type'] == 'practicelab':
                        lab = {"name": course.pop('title'), "description": course.pop('product_id')}
                        obj, created = lab.objects.update_or_create(name=lab["name"], defaults=lab)
    except Exception as exc:
        logger.exception(exc)

@shared_task(bind=True,name="sync_valueed_calender_events")
def sync_valueed_calender_events(self, *args, **kwargs):
    api_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6ImFwaUtleSIsImF1dGgiOiJWYWx1ZUVkSW4ifQeyJuYW1lIjoiRGV2aWFyZSIsImluZGV4IjoxfQw5oThv8fCqCkun6BlldBbpHD7wmZoPflm3mqZqY7POw'
    headers = {'api-key': api_key}
    response = requests.get("https://value-ed-api-backend.vercel.app/scheduled_meetings?page_size=300", headers=headers)
    events_data = []
    if response.status_code == 200:
        events_data = json.loads(response.text).get("response", [])
    work_readiness_course_name = {"Navigating Your Job Search": "Navigating Your Job Search - Work readiness",
                                  "Business and Executive Coaching": "Business & Executive Coaching - Work readiness",
                                  "Building Sales Relationships": "Building Sales Relationships - Work readiness",
                                  "Social Media and Personal Branding": "Social Media and Personal Branding - Work readiness",
                                  "Self Mastery": "Self Mastery - Work readiness",
                                  "Work Readiness" : "Work Readiness Course - Work readiness",
                                  "Mental Health and Wellness": "Mental Health and Wellness - Work readiness"}
    for event in events_data:
        print(f"Checking the presence of the -- Event -- {event}")
        event_exist = Event.objects.filter(event_unique_id=event["uuid"]).exists()
        if datetime.strptime(event["start_time"], "%Y-%m-%dT%H:%M:%SZ") >= datetime.now() and not event_exist and \
                event["topic"].strip() in work_readiness_course_name.keys():
            print(f"New event is being added --- {event}")
            course = Course.objects.filter(name=work_readiness_course_name[event["topic"].strip()]).first()
            start_date = datetime.strptime(event["start_time"], "%Y-%m-%dT%H:%M:%SZ")
            duration_minutes = event["duration"]
            end_time = start_date + timedelta(minutes=duration_minutes)
            try:
                event = Event(
                    name=event["topic"].strip(),
                    description=event["agenda"],
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_time.strftime("%Y-%m-%d"),
                    expiry_date=end_time.strftime("%Y-%m-%d"),
                    start_time=start_date,
                    end_time=end_time,
                    repeat_mode="Single",
                    main_type="Event",
                    sub_type="Course",
                    url=event["join_url"],
                    note='',
                    event_project=None,
                    event_course=course,
                    course_activity="On Zoom",
                    event_assessment=None,
                    assessment_activity=None,
                    event_apprenticeship=None,
                    apprenticeship_activity=None,
                    created_at=datetime.now().strftime("%d %B %Y"),
                    updated_at=datetime.now().strftime("%d %B %Y"),
                    created_by=None,
                    event_unique_id=event["uuid"],
                    event_date_list=start_date.strftime("%Y-%m-%d")
                )
                event.save()
                print("Event Created!")
            except Exception as exc:
                print("ERROR",exc)
                logger.exception(exc)


def get_current_timestamp():
    s_time = int(datetime.timestamp(datetime.now(india_tz) - timedelta(days=1)))
    return int(s_time)


def get_previous_timestamp():
    s_time = int(datetime.timestamp(datetime.now(india_tz) - timedelta(days=272)))
    print("timestamp for last year", s_time)
    # s_time = str(s_time)+"000"
    return int(s_time)

def new_get_previous_timestamp():
    # 293 days ago from today (13 April 2025) is 24 June 2024
    s_time = int(datetime.timestamp(datetime.now(india_tz) - timedelta(days=293)))
    print("Timestamp for 24 June 2024:", s_time)
    return s_time

def get_header():
    headers = {
        'authority': 'login-deviare.lms.simplilearn.com',
        'sec-ch-ua': '"Chromium";v="92", " Not A;Brand";v="99", "Google Chrome";v="92"',
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'x-newrelic-id': 'VQYAWFFWDxAIVVBRAwkO',
        'x-requested-with': 'XMLHttpRequest',
        'sec-ch-ua-mobile': '?0',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36',
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'origin': 'https://login-deviare.lms.simplilearn.com',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': 'https://login-deviare.lms.simplilearn.com/affiliate/user/login',
        'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'cookie': 'PHPSESSID=c9pun0qlb8f0j7canddra90016;'
    }
    return headers

def get_header_deviare():
    headers = {
        'authority': 'deviare-login.lms.simplilearn.com',
        'sec-ch-ua': '"Chromium";v="92", " Not A;Brand";v="99", "Google Chrome";v="92"',
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'x-newrelic-id': 'VQYAWFFWDxAIVVBRAwkO',
        'x-requested-with': 'XMLHttpRequest',
        'sec-ch-ua-mobile': '?0',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36',
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'origin': 'https://deviare-login.lms.simplilearn.com',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': 'https://deviare-login.lms.simplilearn.com/affiliate/user/login',
        'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'cookie': 'PHPSESSID=c9pun0qlb8f0j7canddra90016;'
    }
    return headers


def login_user():
    headers = get_header()

    data = {"user_login": "<EMAIL>", "user_pwd": "Simpli@123", "redirect_url": "/"}
    url = 'https://login-deviare.lms.simplilearn.com/affiliate/process/authenticate-user'

    response = requests.post(url, headers=headers, data=data)
    print("response:::::",response)
    return response
def deviare_login_user():
    headers = get_header_deviare()

    data = {"user_login": "<EMAIL>", "user_pwd": "Simpli@123", "redirect_url": "/"}
    url = 'https://deviare-login.lms.simplilearn.com/affiliate/process/authenticate-user'

    response = requests.post(url, headers=headers, data=data)
    return response    

def new_simplilearn_course(cookies):
    url = "https://deviare-login.lms.simplilearn.com/affiliate/enterprise-report/download"
    payload = "{\"report_type_id\":\"2\",\"report_name\":\"Learner Activity\",\"action\":\"download\",\"filters\":{\"couseAssignmentStartDate\":" + str(
        new_get_previous_timestamp()) + ",\"courseAssignmentEndDate\":" + str(
        get_current_timestamp()) + ",\"isExcludeExpiredParam\":false}}"
    headers = {
        'cookie': 'PHPSESSID=c9pun0qlb8f0j7canddra90016;' + cookies,
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response)
    return response

def download_id(cookies):
    url = "https://login-deviare.lms.simplilearn.com/affiliate/enterprise-report/download"
    payload = "{\"report_type_id\":\"2\",\"report_name\":\"Learner Activity\",\"action\":\"download\",\"filters\":{\"couseAssignmentStartDate\":" + str(
        get_previous_timestamp()) + ",\"courseAssignmentEndDate\":" + str(
        get_current_timestamp()) + ",\"isExcludeExpiredParam\":false}}"
    headers = {
        'cookie': 'PHPSESSID=c9pun0qlb8f0j7canddra90016;' + cookies,
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text)
    return response


def is_float(i):
    try:
        float(i)
        return True
    except ValueError:
        return False


def get_project_status(keys, row):
    first_project = keys.index('Project Name 1')
    last_project = keys.index('Submit Date 116')
    return 'Passed' if len(
        [project for project in range(first_project, last_project + 1) if
         str(row[project]).lower() == 'passed']) else 'Pending'


def get_test_score(keys, row):
    first_score = keys.index('Assessment Test 1') + 1
    all_index = []
    all_scores = []
    for i in range(6):
        all_index.append(first_score)
        if is_float(row[first_score]):
            all_scores.append(float(row[first_score]))
        first_score += 3
    return max(all_scores) if all_scores else 0


def get_or_create_user(email, course):
    data = {"email": email, "course_title": course}
    try:
        user = ElearninStates.objects.get(email=email, course_title=course)
    except:
        user = ElearninStates.objects.create(**data)
    return user


def ge_live_class_attendence(keys, row):
    key_len = len(keys)
    first = keys.index('Exclusive Live Class - 1841795055')
    # first = keys.index('Exclusive Live Class - 576743770')

    all_ele = [row[x] for x in range(first, key_len) if is_float(row[x])]
    return max(all_ele) if all_ele else 0

def neww_ge_live_class_attendence(keys, row):
    key_len = len(keys)
    first = keys.index('Exclusive Live Class - 99285115737')
    # Exclusive Live Class - 99285115737

    all_ele = [row[x] for x in range(first, key_len) if is_float(row[x])]
    return max(all_ele) if all_ele else 0

# Old simplilearn portal data fetch
@shared_task(bind=True, name="download_report_from_simplelearn")
def download_report_from_simplelearn(request):
    login_response = login_user()

    if login_response.status_code == 200:
        download_status = download_id(login_response.headers['Set-Cookie'])
        if download_status.status_code == 200 and 'download_id' in eval(download_status.text.replace('true', '200'))[
            'data']:
            d_id = eval(download_status.text.replace('true', '200'))['data']['download_id']
            headers = get_header()
            headers['cookie'] = headers['cookie'] + login_response.headers['Set-Cookie']
            data = '{"0":{"download_id":0,"report_name":"License Consumption"},"1":{"download_id":' + d_id + ',"report_name":"Learner Activity"},"2":{"download_id":0,"report_name":"Progress Overview"},"3":{"download_id":0,"report_name":"Course Completion"}}'
            while True:
                response = requests.post(
                    'https://login-deviare.lms.simplilearn.com/affiliate/enterprise-report/check-download-status',
                    headers=headers, data=data)
                file_list = eval(response.text.replace('false', '0').replace('true', '1'))['data']
                file_path = [i['file_path'] for i in file_list if 'file_path' in i]
                if file_path != [] and file_path[0] != 0:
                    file_path = file_path[0]
                    fname = os.path.basename(file_path)
                    url = file_path.replace('\\', '')
                    r = requests.get(url, stream=True)
                    with open(fname, 'wb') as f:
                        os.chmod(fname, 0o777)
                        total_length = int(r.headers.get('content-length'))
                        for chunk in progress.bar(r.iter_content(chunk_size=1024),
                                                  expected_size=(total_length / 1024) + 1):
                            if chunk:
                                f.write(chunk)
                                f.flush()
                    print("tetst====>")
                    upload_data = upload_csv(fname)
                    break
                else:
                    logger.critical(download_status.text)

        else:
            logger.critical(download_status.text)
    else:
        logger.critical(login_response.text)


#  New simplilearn portal data fetch

@shared_task(bind=True, name="deviare_download_report_from_simplelearn")
def deviare_download_report_from_simplelearn(request):
    login_response = deviare_login_user()

    if login_response.status_code == 200:
        download_status = new_simplilearn_course(login_response.headers['Set-Cookie'])
        if download_status.status_code == 200 and 'download_id' in eval(download_status.text.replace('true', '200'))[
            'data']:
            d_id = eval(download_status.text.replace('true', '200'))['data']['download_id']
            # calling header for deviare login
            headers = get_header_deviare()
            headers['cookie'] = headers['cookie'] + login_response.headers['Set-Cookie']
            data = '{"0":{"download_id":0,"report_name":"License Consumption"},"1":{"download_id":' + d_id + ',"report_name":"Learner Activity"},"2":{"download_id":0,"report_name":"Progress Overview"},"3":{"download_id":0,"report_name":"Course Completion"}}'

            while True:
                response = requests.post(
                    'https://deviare-login.lms.simplilearn.com/affiliate/enterprise-report/check-download-status',
                    headers=headers, data=data)
                response_data = json.loads(response.text.replace('false', '0').replace('true', '1'))
                file_list = response_data['data']
                file_path = [i['file_path'] for i in file_list if 'file_path' in i]
                
                if file_path != [] and file_path[0] != 0:
                    print("Inside if statement::file_path")
                    file_path = file_path[0]
                    fname = os.path.basename(file_path)
                    url = file_path.replace('\\', '')
                    r = requests.get(url, stream=True)
                    with open(fname, 'wb') as f:
                        os.chmod(fname, 0o777)
                        total_length = int(r.headers.get('content-length'))
                        for chunk in progress.bar(r.iter_content(chunk_size=1024),
                                                  expected_size=(total_length / 1024) + 1):
                            if chunk:
                                f.write(chunk)
                                f.flush()
                    print("tetst====>")
                    upload_data = devaire_upload_csv(fname)
                    break
                else:
                    logger.critical(download_status.text)

        else:
            logger.critical(download_status.text)
    else:
        logger.critical(login_response.text)


def upload_csv(filename):
    # filename = "Deviare_Learner_Activity_2021_12_06_EST.old.csv"
    try:
        f = open(filename, 'r', encoding="utf-8")
        reader = csv.reader(f)
        lt = []
        for rw in reader:
            keys = rw
            break
        # Ignoring the header
        next(reader)
        for row in reader:
            new_dict = dict(zip(keys, row))
            user = get_or_create_user(new_dict['Learner Email'], new_dict['Course Name'])
            user.name = new_dict['Learner Name']
            primary_user = None
            secondary_user = None
            try:
                if len(new_dict['Learner Name'].split(',')) == 2:
                    fname = new_dict['Learner Name'].split(',')[-1]
                    lname = new_dict['Learner Name'].split(',')[0]
                    match = re.search(r'_(\d+)@', new_dict['Learner Email'])
                    if match:
                        talent_lms_id = match.group(1)
                    else:
                        talent_lms_id = None
                    try:
                        primary_user = UserSettings.objects.get(firstName = fname, lastName = lname, user_id_talentlms= talent_lms_id)
                        print(f"primary_user - {primary_user}")
                        to_email = primary_user.email
                        secondary_user = User.objects.get(email=new_dict['Learner Email'])
                        print("secondary_user::::", secondary_user)
                    except:
                        pass

                else:
                    primary_user = UserSettings.objects.get(email=new_dict['Learner Name'])
                    to_email = primary_user.email
                    secondary_user = User.objects.get(email=new_dict['Learner Email'])
            except:
                pass
            user.user = primary_user
            user.final_users_new = secondary_user
            print('primary_user: ', primary_user)
            user.account_status = new_dict['Account Status']
            user.order_type = new_dict['Order Type']
            if new_dict['Team'] != "NA":
                if ElearningProject.objects.filter(name=new_dict['Team']).exists():
                    team = ElearningProject.objects.get(name=new_dict['Team'])
                else:
                    x = ElearningProject(name=new_dict['Team'])
                    x.save()
                    team = x
                user.team.add(team)
            user.activity_level = new_dict["Activity Level"]
            user.course_assignment_date = new_dict['Course Assignment Date']
            user.course_activation_date = new_dict['Course Activation Date'] if new_dict[
                                                                                    'Course Activation Date'] != 'NA' else None
            user.course_type = new_dict['Course Type']
            user.course_id = int(new_dict['Course Id'])
            user.self_learning_completion = float(new_dict['Self-Learning Completion %'])
            user.course_expiration_date = new_dict['Course Expiration Date']
            user.course_title = new_dict['Course Name']
            user.test_score = float(get_test_score(keys, row))
            user.project_result = get_project_status(keys, row)
            user.course_completion_date = new_dict['Certificate Unlock Date']
            user.live_class_attended = float(ge_live_class_attendence(keys, row))  # new_dict['Learning days']
            user.osl_score = float(new_dict['Self-Learning Completion %']) if not new_dict[
                                                                                      'Self-Learning Completion %'] == 'NA' else 0 * 0.01
            user.lvc_sore = float(new_dict['Learning days']) if not new_dict[
                                                                        'Learning days'] == 'NA' else 0 * 0.01  # todo
            user.project_score = 10 if user.project_result.lower() == "passed" else 0
            user.test_score_second = user.test_score * 0.1
            user.certification_score = 0 if user.course_activation_date else 50
            user.concat = str(new_dict['Learner Email']) + str(new_dict['Course Id'])
            user.program = new_dict['Course Name']
            user.total_score = float(user.certification_score) + float(user.live_class_attended)
            user.certification_status = 'Certified' if new_dict['Certificate Unlock Date'] != 'NA' else 'Not Certified'
            user.last_login_date = new_dict['Last Login Date']
            user.last_activity_on = new_dict['Last Activity On']
            user.self_learning_time = new_dict['Self-Learning Time']
            user.course_access = new_dict['Course Access']
            user.program_id = new_dict['Program Id']
            user.enrolment_cohort_id = new_dict['Enrolment Cohort ID']
            user.enrolment_cohort_name = new_dict['Enrolment Cohort Name']
            user.current_cohort_name = new_dict['Current Cohort Name']
            user.current_cohort_start_date = new_dict['Current Cohort Start Date']
            user.current_cohort_end_date = new_dict['Current Cohort End Date']
            user.current_cohort_id = new_dict['Current Cohort ID']
            user.cohort_enrollment_date = new_dict['Cohort Enrollment Date']
            user.overall_classess = new_dict['Classes Completed/Overall Classes']
            user.mentoring_registered = new_dict['Mentoring Registered']
            user.mentoring_attended = new_dict['Mentoring Attended']
            user.live_classes_registered = new_dict['Live Classes Registered']
            user.live_sessions_attended = new_dict['Live Sessions Attended']
            user.assessment = user.test_score
            print(str(user.email) + " before")
            user.save()
            # if user.certification_status == "Certified" and not user.is_email_sent:
            #     body = (
            #         "<p>Dear Deviare Graduate,</p>"
            #         f"<p>Congratulations on successfully completing your {user.course_title} with us. "
            #         "We hope your experience with Deviare has accelerated your ambitions towards a successful and impactful digital career.</p>"
            #         "<p>We are delighted to invite you to be part of our Talent Platform as the next phase of your career journey. "
            #         "At Deviare, we work hard to connect our graduates to real work opportunities through our network of clients and partners. "
            #         "The Talent Platform is an efficient way for you to find work that aligns with your goals. Moreover, it is an efficient way for potential employers to find you.</p>"
            #         "<p>Find out more about the Talent Platform here: "
            #         "<a href='https://www.deviare.co.za/talent'>https://www.deviare.co.za/talent</a></p>"
            #         "<p>We already have the bulk of your information, and by clicking on this link "
            #         "<a href='https://talent.deviare.africa'>https://talent.deviare.africa</a>, start the journey towards your dream job.</p>"
            #         "<p>We hope to see you on the Talent Platform and look forward to being part of your career journey.</p>"
            #         "<p>Best wishes,</p>"
            #         "<p>Deviare Talent Team</p>"
            #     )
            #     subject = f"Congratulations on Completing {user.course_title}!"
            #     try:
            #         FROM = "<EMAIL>"
            #         TO = to_email if type(to_email) is list else [to_email]
            #         msg = send_mail(subject, body, FROM, TO, html_message=body, fail_silently=False)
            #         user.is_email_sent = True
            #         user.save()
            #     except Exception as e:
            #         print("Error while sending an email:", e)
            #         logger.exception(e)
            #         pass
            print(str(user.email) + " saved")
        logger.critical(filename)
        os.remove(filename)


    except Exception as exc:
        print("error=====>", exc)
        logger.exception(exc)

# upload csv for new simplilearn portal

@transaction.atomic
def devaire_upload_csv(filename):
    try:
        f = open(filename, 'r', encoding="utf-8")
        reader = csv.reader(f)
        lt = []

        # Extract keys (header)
        for rw in reader:
            keys = rw
            break

        # Ignore second header if present
        next(reader)

        for row in reader:
            new_dict = dict(zip(keys, row))

            if not new_dict['Learner Email'] or not new_dict['Course Name']:
                print(f"Skipping row with missing email or course: {new_dict}")
                continue

            user = get_or_create_user(new_dict['Learner Email'], new_dict['Course Name'])
            # print("user::::---->", user)

            try:
                primary_user = UserSettings.objects.get(email__iexact=new_dict['Learner Email'])
                print(f"primary_user - {primary_user}")
            except UserSettings.DoesNotExist:
                # print(f"No UserSettings found for {new_dict['Learner Email']}")
                continue

            secondary_user = primary_user.user
            if not secondary_user:
                print(f"UserSettings for {new_dict['Learner Email']} has no associated User")
                continue

            user.user = primary_user
            user.final_users_new = secondary_user

            user.name = new_dict['Learner Name']
            user.account_status = new_dict['Account Status']
            user.order_type = new_dict['Order Type']

            # Team assignment
            if new_dict['Team'] != "NA":
                team, _ = ElearningProject.objects.get_or_create(name=new_dict['Team'])
                user.team.add(team)

            user.activity_level = new_dict["Activity Level"]
            user.course_assignment_date = new_dict['Course Assignment Date']
            user.course_activation_date = new_dict['Course Activation Date'] if new_dict['Course Activation Date'] != 'NA' else None
            user.course_type = new_dict['Course Type']
            user.course_id = int(new_dict['Course Id'])
            user.self_learning_completion = float(new_dict['Self-Learning Completion %'])
            user.course_expiration_date = new_dict['Course Expiration Date']
            user.course_title = new_dict['Course Name']
            user.test_score = float(get_test_score(keys, row))
            user.project_result = get_project_status(keys, row)
            user.course_completion_date = new_dict['Certificate Unlock Date']
            user.live_class_attended = float(neww_ge_live_class_attendence(keys, row))
            user.osl_score = float(new_dict['Self-Learning Completion %']) if new_dict['Self-Learning Completion %'] != 'NA' else 0 * 0.01
            user.lvc_sore = float(new_dict['Learning days']) if new_dict['Learning days'] != 'NA' else 0 * 0.01
            user.project_score = 10 if user.project_result.lower() == "passed" else 0
            user.test_score_second = user.test_score * 0.1
            user.certification_score = 0 if user.course_activation_date else 50
            user.concat = str(new_dict['Learner Email']) + str(new_dict['Course Id'])
            user.program = new_dict['Course Name']
            user.total_score = float(user.certification_score) + float(user.live_class_attended)
            user.certification_status = 'Certified' if new_dict['Certificate Unlock Date'] != 'NA' else 'Not Certified'
            user.last_login_date = new_dict['Last Login Date']
            user.last_activity_on = new_dict['Last Activity On']
            user.self_learning_time = new_dict['Self-Learning Time']
            user.course_access = new_dict['Course Access']
            user.program_id = new_dict['Program Id']
            user.enrolment_cohort_id = new_dict['Enrolment Cohort ID']
            user.enrolment_cohort_name = new_dict['Enrolment Cohort Name']
            user.current_cohort_name = new_dict['Current Cohort Name']
            user.current_cohort_start_date = new_dict['Current Cohort Start Date']
            user.current_cohort_end_date = new_dict['Current Cohort End Date']
            user.current_cohort_id = new_dict['Current Cohort ID']
            user.cohort_enrollment_date = new_dict['Cohort Enrollment Date']
            user.overall_classess = new_dict['Classes Completed/Overall Classes']
            user.mentoring_registered = new_dict['Mentoring Registered']
            user.mentoring_attended = new_dict['Mentoring Attended']
            user.live_classes_registered = new_dict['Live Classes Registered']
            user.live_sessions_attended = new_dict['Live Sessions Attended']
            user.assessment = user.test_score

            # print(str(user.email) + " before")
            user.save()

            # # ✅ Email logic
            # if user.certification_status == "Certified" and not user.is_email_sent:
            #     body = (
            #         "<p>Dear Deviare Graduate,</p>"
            #         f"<p>Congratulations on successfully completing your {user.course_title} with us. "
            #         "We hope your experience with Deviare has accelerated your ambitions towards a successful and impactful digital career.</p>"
            #         "<p>We are delighted to invite you to be part of our Talent Platform as the next phase of your career journey. "
            #         "At Deviare, we work hard to connect our graduates to real work opportunities through our network of clients and partners. "
            #         "The Talent Platform is an efficient way for you to find work that aligns with your goals. Moreover, it is an efficient way for potential employers to find you.</p>"
            #         "<p>Find out more about the Talent Platform here: "
            #         "<a href='https://www.deviare.co.za/talent'>https://www.deviare.co.za/talent</a></p>"
            #         "<p>We already have the bulk of your information, and by clicking on this link "
            #         "<a href='https://talent.deviare.africa'>https://talent.deviare.africa</a>, start the journey towards your dream job.</p>"
            #         "<p>We hope to see you on the Talent Platform and look forward to being part of your career journey.</p>"
            #         "<p>Best wishes,</p>"
            #         "<p>Deviare Talent Team</p>"
            #     )
            #     subject = f"Congratulations on Completing {user.course_title}!"
            #     try:
            #         FROM = "<EMAIL>"
            #         TO = [primary_user.email]
            #         send_mail(subject, body, FROM, TO, html_message=body, fail_silently=False)
            #         user.is_email_sent = True
            #         user.save()
            #     except Exception as e:
            #         print("Error while sending an email:", e)
            #         logger.exception(e)
            #         pass

            # print(str(user.email) + " saved")

        logger.critical(filename)
        os.remove(filename)

    except Exception as exc:
        print("error=====>", exc)
        logger.exception(exc)

# def devaire_upload_csv(filename):
#     try:
#         with open(filename, 'r', encoding="utf-8") as f:
#             reader = csv.DictReader(f)
#             for row in reader:
#                 try:
#                     # Fetch or create the user based on the Learner Email
#                     user = get_or_create_user(row['Learner Email'], row['Course Name'])

#                     # Fetch UserSettings object
#                     primary_user = UserSettings.objects.get(email__iexact=row['Learner Email'])
#                     print(f"primary_user - {primary_user}")

#                     # Fetch associated User object from UserSettings
#                     secondary_user = primary_user.user
#                     if secondary_user is None:
#                         print(f"UserSettings for {row['Learner Email']} has no associated User")
#                         continue  # Skip this row or handle differently

#                     # Set foreign keys in ElearninStates
#                     user.user = primary_user
#                     user.final_users_new = secondary_user
                    
#                     # Populate user attributes directly from the row data
#                     user.name = row['Learner Name']
#                     user.email = row['Learner Email']
#                     user.account_status = row['Account Status']
#                     user.order_type = row['Order Type']
#                     user.activity_level = row["Activity Level"]
#                     user.course_assignment_date = row['Course Assignment Date']
#                     user.course_activation_date = row['Course Activation Date'] if row['Course Activation Date'] != 'NA' else None
#                     user.course_type = row['Course Type']
#                     user.course_id = int(row['Course Id'])
#                     user.self_learning_completion = float(row['Self-Learning Completion %'])
#                     user.course_expiration_date = row['Course Expiration Date']
#                     user.course_title = row['Course Name']
#                     user.test_score = float(row['Test Score']) if 'Test Score' in row else 0
#                     user.project_result = row['Project Result'] if 'Project Result' in row else "Not Available"
#                     user.course_completion_date = row['Certificate Unlock Date']
#                     user.live_class_attended = float(row['Live Class Attended']) if 'Live Class Attended' in row else 0
#                     user.osl_score = user.self_learning_completion * 0.01
#                     user.certification_status = 'Certified' if row['Certificate Unlock Date'] != 'NA' else 'Not Certified'
#                     user.last_login_date = row['Last Login Date']
#                     user.last_activity_on = row['Last Activity On']
#                     user.self_learning_time = row['Self-Learning Time']
#                     user.course_access = row['Course Access']
#                     user.program = row['Course Name']
#                     user.total_score = float(user.certification_status == 'Certified') * 100
#                     user.save()

#                     # Check if a congratulatory email needs to be sent
#                     if user.certification_status == "Certified" and not user.is_email_sent:
#                         body = (
#                             "<p>Dear Deviare Graduate,</p>"
#                             f"<p>Congratulations on successfully completing your {user.course_title} with us. "
#                             "We hope your experience with Deviare has accelerated your ambitions towards a successful and impactful digital career.</p>"
#                             "<p>We are delighted to invite you to be part of our Talent Platform as the next phase of your career journey. "
#                             "At Deviare, we work hard to connect our graduates to real work opportunities through our network of clients and partners. "
#                             "The Talent Platform is an efficient way for you to find work that aligns with your goals. Moreover, it is an efficient way for potential employers to find you.</p>"
#                             "<p>Find out more about the Talent Platform here: "
#                             "<a href='https://www.deviare.co.za/talent'>https://www.deviare.co.za/talent</a></p>"
#                             "<p>We already have the bulk of your information, and by clicking on this link "
#                             "<a href='https://talent.deviare.africa'>https://talent.deviare.africa</a>, start the journey towards your dream job.</p>"
#                             "<p>We hope to see you on the Talent Platform and look forward to being part of your career journey.</p>"
#                             "<p>Best wishes,</p>"
#                             "<p>Deviare Talent Team</p>"
#                         )
#                         subject = f"Congratulations on Completing {user.course_title}!"
#                         try:
#                             FROM = "<EMAIL>"
#                             TO = user.email
#                             send_mail(subject, body, FROM, [TO], html_message=body, fail_silently=False)
#                             user.is_email_sent = True
#                             user.save()
#                         except Exception as e:
#                             print("Error while sending an email:", e)
#                             logger.exception(e)
#                             pass

#                     print(f"{user.email} saved successfully.")

#                 except Exception as user_error:
#                     print(f"Error processing user {row['Learner Email']}: {user_error}")
#                     logger.exception(user_error)

#         print("CSV file processed successfully.")
#         logger.critical(filename)
#         os.remove(filename)
#     except Exception as file_error:
#         print(f"Error reading CSV file: {file_error}")
 
def change_user_status_to_inactive(user_id):
    """
    Change User Status To Inactive
    """
    try:

        url = URL_ADDRESS + "/usersetstatus/user_id:" + user_id + ",status:inactive"
        response = requests.request("POST", url, auth=(API_KEY, ""), headers={}, files=[])
        print(response.text)
        if response.status_code == 200:
            logger.info("User Inactive" + user_id)
        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


def change_user_status_to_active(user_id):
    """
    Change User Status To Inactive
    """
    try:

        url = URL_ADDRESS + "/usersetstatus/user_id:" + user_id + ",status:active"
        response = requests.request("POST", url, auth=(API_KEY, ""), headers={}, files=[])
        print(response.text)
        if response.status_code == 200:
            logger.info("User Inactive" + user_id)
        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


def change_user_status_to_inactive(user_id):
    """
    Change User Status To Inactive
    """
    try:

        url = URL_ADDRESS + "/usersetstatus/user_id:" + user_id + ",status:inactive"
        response = requests.request("POST", url, auth=(API_KEY, ""), headers={}, files=[])
        print(response.text)
        if response.status_code == 200:
            logger.info("User Inactive" + user_id)
        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


def change_user_status_to_active(user_id):
    """
    Change User Status To Inactive
    """
    try:

        url = URL_ADDRESS + "/usersetstatus/user_id:" + user_id + ",status:active"
        response = requests.request("POST", url, auth=(API_KEY, ""), headers={}, files=[])
        print(response.text)
        if response.status_code == 200:
            logger.info("User Inactive" + user_id)
        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


@shared_task(bind=True, name="assign_user_to_course")
def assign_user_to_course_on_talent_lms(self, *args, **kwargs):
    """
    Add user to course on talent LMS platform
    """
    try:
        for course in kwargs['kwargs']["courses"]:
            course_uuid = course["course_id"]
            users = [user["uuid"] for user in course["user"]]
            if users:
                assigned = CourseLicenseUser.objects.filter(
                    user_id__in=users, course_license_id__course_id=course_uuid
                )

                # By the time task runs, this objects would
                # have already been deleted
                # unassigned = CourseLicenseUser.objects.filter(user_id__in=users).exclude(user_id__in=users)
                for entry in assigned:
                    # Retrieve identifiers provided by talent LMS
                    course_id = entry.course_license_id.course_id.course_id_talent_lms
                    user_id = entry.user_id.user_id_talentlms

                    if course_id:
                        if not user_id:
                            user = usersignup(entry.user_id)
                            user_id = user.user_id_talentlms
                        if user_id:
                            response = add_user_to_course(course_id=course_id, user_id=user_id)
                            if response is not None:
                                if response.status_code == 200:
                                    logger.info("User successfully assigned to course")
                                else:
                                    logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


@shared_task(bind=True, name="add_user")
def add_user_to_talent_lms(self, *args, **kwargs):
    """
    Add user to talent LMS
    """
    if "lastName" not in kwargs['kwargs']:
        kwargs['kwargs']["lastName"] = kwargs['kwargs']["firstName"]

    try:
        url = "%s/usersignup" % URL_ADDRESS
        payload = {
            "first_name": kwargs['kwargs']["firstName"],
            "last_name": kwargs['kwargs']["lastName"],
            # "email": 'dnedit__'+kwargs['kwargs']['email'],
            "email": kwargs['kwargs']['email'],
            "login": kwargs['kwargs']["userName"],
            "password": kwargs['kwargs'].get("unencrypted_password", "icecream1781"),
        }
        files = []
        headers = {}

        response = requests.request(
            "POST",
            url,
            auth=HTTPBasicAuth(API_KEY, ""),
            headers=headers,
            data=payload,
            files=files,
        )

        if response.status_code == 200:
            # User successfully created on TalentLMS
            content = json.loads(response.text)
            user = UserSettings.objects.get(email=kwargs['kwargs']["email"])
            user.user_id_talentlms = content["id"]
            user.save()
            return content["id"]
        user = UserSettings.objects.get(email=kwargs['kwargs']['email'])
        if user.user_id_talentlms == None:
            url = URL_ADDRESS + "/users"
            response = requests.request('GET', url, auth=HTTPBasicAuth(API_KEY, ""), headers={})
            user_data = [i for i in eval(response.text.replace("null", "1234")) if i['login'] == user.email]
            if user_data != []:
                user.user_id_talentlms = user_data[0]["id"]
                user.save()

        else:
            logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


@shared_task(bind=True)
def edit_user_profile_on_talent_lms(self, *args, **kwargs):
    """
    Update user profile on talent lms
    """
    try:

        url = "%s/edituser" % URL_ADDRESS
        user_id = kwargs.get("user_id")
        password = kwargs.get("password")
        if user_id and password:

            payload = {"user_id": user_id, "password": password}

            response = requests.request("POST", url, auth=(API_KEY, ""), headers={}, data=payload, files=[])

            if response.status_code == 200:
                logger.info("password reset")

            else:
                logger.critical(response.text)

    except Exception as exc:
        logger.exception(exc)


@shared_task(bind=True, name="retrieve_reports_from_mail")
def get_user_reports_from_mail(self):
    """
    Retrieve SimpliLearn user reports from
    mail server
    """

    try:

        link = get_link()
        logger.info(link)
        if not link:
            raise Exception

        df = pd.read_csv(link, low_memory=False)
        df = df.replace(to_replace="Not Assigned", value=np.nan)
        df = df.dropna(axis="columns", how="all")

        entries = CourseLicenseUser.objects.all().values(
            "user_id__email", "course_license_id__course_id__course_id")

        user_courses = []
        for e in list(entries):
            email = e["user_id__email"]
            val = e["course_license_id__course_id__course_id"]
            if not val:
                val = "-1"

            c = email + val
            user_courses.append(c)

        # Filter reports with existing course user license
        # information
        df["user_course"] = df["Learner Email"] + df["Course Id"].astype(str)
        df = df[df.user_course.isin(user_courses)]
        report = df.drop(columns=["user_course"])

        results = []
        for index, entry in report.iterrows():
            row = entry.dropna()
            # Drop columns and rows with unavailable data
            results.append(row.to_dict())

        duplicate_licences = []
        with transaction.atomic():
            for entry in results:
                email = entry["Learner Email"]
                course_id = entry["Course Id"]

                try:
                    course_completion = float(entry["Self-Learning Completion %"])
                    cl = CourseLicenseUser.objects.get(
                        user_id__email=email, course_license_id__course_id__course_id=course_id
                    )
                    cl.course_completion = course_completion
                    cl.save()

                    UserReport.objects.update_or_create(user_license=cl, defaults={"report": entry})

                except CourseLicenseUser.MultipleObjectsReturned:
                    duplicate_licences.append(cl.pk)
                    pass

                except Exception as e:
                    logger.exception(e)
                    raise

        logger.info("Duplicate licenses")
        logger.info(duplicate_licences)

    except Exception as exc:
        logger.exception(exc)


# @shared_task(bind=True, name="retrieve_reports_from_lms")
# def get_user_course_report_on_talent_lms(self):
#     try:
#         # Get course licenses where provider is talent lms
#         course_licenses = CourseLicenseUser.objects.select_related("course_license_id", "user_id").filter(
#             course_license_id__course_id__course_id_talent_lms__isnull=False
#         )

#         for course_license in course_licenses:
#             course_id = course_license.course_license_id.course_id.course_id_talent_lms
#             user_id = course_license.user_id.user_id_talentlms

#             url = "%s/getuserstatusincourse/course_id:%s,user_id:%s" % (URL_ADDRESS, course_id, user_id)

#             try:
#                 response = requests.request("GET", url, auth=(API_KEY, ""), headers={}, data={}, files=[])
#                 if response.status_code == 200:
#                     # Update user report information
#                     entry = json.loads(response.text)
#                     entry["provider"] = "devaire_talent_lms"

#                     course_license.course_completion = entry["completion_percentage"]
#                     course_license.save()

#                     UserReport.objects.update_or_create(user_license=course_license, defaults={"report": entry})

#             except Exception as exc:
#                 logger.exception(exc)
#                 continue

#     except Exception as exc:
#         logger.exception(exc)


def get_auth_header(api_key):
    """Generate Base64-encoded Basic Auth header."""
    encoded = base64.b64encode(f"{api_key}:".encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def fetch_user_course_status(course_id, user_id):
    """
    Fetch and update the detailed report for a specific user in a course from TalentLMS.
    """
    try:
        if not course_id or not str(course_id).isdigit():
            print(f"Invalid course_id: {course_id}. Skipping.")
            return

        if not user_id or not str(user_id).isdigit():
            print(f"Invalid user_id: {user_id}. Skipping.")
            return

        # Check if user already has a completed record
        try:
            existing = ElearninStates.objects.get(
                user__user_id_talentlms=user_id,
                course_id=int(course_id)
            )
            if existing.self_learning_completion == 100:
                print(f"Skipping user_id={user_id} for course_id={course_id} (already completed with {existing.self_learning_completion}%).")
                return
        except ElearninStates.DoesNotExist:
            existing = None

        url = f"{URL_ADDRESS}/getuserstatusincourse/course_id:{course_id},user_id:{user_id}"
        headers = get_auth_header(API_KEY)
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 404:
            logger.warning(f"Course {course_id} or user {user_id} does not exist. Skipping.")
            return

        response.raise_for_status()
        user_data = response.json()

        # Fetch the UserSettings and Course objects
        try:
            user_setting = UserSettings.objects.get(user_id_talentlms=user_id)
            user_obj = user_setting.user
            course = Course.objects.get(course_id_talent_lms=course_id)
        except ObjectDoesNotExist as exc:
            print(f"Local course or user not found for course_id:{course_id}, user_id:{user_id}: {exc}")
            return

        # Determine name from UserSettings fields
        first_name = user_setting.firstName or ""
        last_name = user_setting.lastName or ""
        name = f"{first_name} {last_name}".strip() or user_setting.userName or ""

        # Map API response to ElearninStates fields
        completion_percentage = float(user_data.get("completion_percentage", 0))
        certification_status = "Certified" if user_data.get("completion_status") == "Completed" else "Not Certified"
        project_result = "passed" if completion_percentage == 100 else "pending"

        # Check if update is actually needed (avoid unnecessary updates)
        if existing and existing.self_learning_completion == completion_percentage:
            print(f"Skipping user_id={user_id} for course_id={course_id} (completion unchanged at {completion_percentage}%).")
            return

        enrolled_on = user_data.get("enrolled_on", "")
        completed_on = user_data.get("completed_on", "")
        expired_on = user_data.get("expired_on", "")
        total_time = user_data.get("total_time", "")

        # Fetch course title from course data
        course_title = course.name if hasattr(course, 'name') else "Unknown Course"

        # Determine email, preferring UserSettings email if available
        email = user_setting.email or (user_setting.user.email if hasattr(user_setting.user, 'email') else "")

        # Use manual transaction management with retry mechanism for database operations
        max_retries = 3
        retry_delay = 1  # seconds

        for attempt in range(max_retries):
            try:
                # Try to get or create the record first, then update if needed
                # Use the same query criteria as the initial check for consistency
                try:
                    existing_record = ElearninStates.objects.get(
                        user__user_id_talentlms=user_id,
                        course_id=int(course_id)
                    )
                    # Update existing record
                    existing_record.updated_at = timezone.now()
                    existing_record.name = name
                    existing_record.email = email
                    existing_record.course_assignment_date = enrolled_on
                    existing_record.course_activation_date = enrolled_on
                    existing_record.course_type = course.course_type if hasattr(course, 'course_type') and course.course_type else "Online Classroom"
                    existing_record.self_learning_completion = completion_percentage
                    existing_record.course_expiration_date = expired_on
                    existing_record.course_title = course_title
                    existing_record.course_completion_date = completed_on
                    existing_record.self_learning_time = total_time
                    existing_record.certification_status = certification_status
                    existing_record.program = course_title
                    existing_record.program_id = str(course_id)
                    existing_record.project_result = project_result
                    existing_record.save()
                    created = False
                    old_completion = existing.self_learning_completion if existing else 0
                    print(f"ElearninStates updated for user_id:{user_id}, course_id:{course_id} (completion: {old_completion}% → {completion_percentage}%)")

                except ElearninStates.DoesNotExist:
                    # Create new record
                    existing_record = ElearninStates.objects.create(
                        user=user_setting,
                        final_users_new=user_obj,
                        course_id=int(course_id),
                        updated_at=timezone.now(),
                        name=name,
                        email=email,
                        course_assignment_date=enrolled_on,
                        course_activation_date=enrolled_on,
                        course_type=course.course_type if hasattr(course, 'course_type') and course.course_type else "Online Classroom",
                        self_learning_completion=completion_percentage,
                        course_expiration_date=expired_on,
                        course_title=course_title,
                        course_completion_date=completed_on,
                        self_learning_time=total_time,
                        certification_status=certification_status,
                        program=course_title,
                        program_id=str(course_id),
                        project_result=project_result
                    )
                    created = True
                    print(f"ElearninStates created for user_id:{user_id}, course_id:{course_id} (completion: {completion_percentage}%)")

                # Update CourseLicenseUser if it exists
                try:
                    existing_clus = CourseLicenseUser.objects.filter(
                        course_license_id__course_id=course,
                        user_id=user_setting
                    )

                    if existing_clus.exists():
                        update_count = existing_clus.update(course_completion=completion_percentage)
                        print(f"Updated {update_count} CourseLicenseUser records for user:{user_id}, course:{course_id}")
                except Exception as e:
                    print(f"Error updating CourseLicenseUser for user:{user_id}, course:{course_id}: {e}")

                # If we reach here, the operation was successful
                break

            except Exception as exc:
                error_msg = str(exc)
                if any(keyword in error_msg for keyword in ["Lock wait timeout exceeded", "Deadlock found", "try restarting transaction"]) and attempt < max_retries - 1:
                    print(f"Database lock issue on attempt {attempt + 1} for user_id:{user_id}, course_id:{course_id}. Error: {error_msg}. Retrying in {retry_delay} seconds...")
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    print(f"Error saving ElearninStates for user_id:{user_id}, course_id:{course_id}: {exc}")
                    if attempt == max_retries - 1:
                        # Only raise on final attempt for non-lock related errors
                        if not any(keyword in error_msg for keyword in ["Lock wait timeout exceeded", "Deadlock found", "try restarting transaction"]):
                            raise
                        else:
                            print(f"Final attempt failed due to database lock for user_id:{user_id}, course_id:{course_id}. Skipping this user.")
                            return

    except requests.RequestException as exc:
        print(f"API request failed for course_id:{course_id}, user_id:{user_id}: {exc}")
    except Exception as exc:
        print(f"Unexpected error for course_id:{course_id}, user_id:{user_id}: {exc}")

def process_course_enrollments(course_id):
    """
    Fetch enrolled users for a course and dispatch tasks for detailed status fetching.
    """
    try:
        if not course_id or not str(course_id).isdigit():
            logger.warning(f"Invalid course_id: {course_id}. Skipping.")
            return

        url = f"{URL_ADDRESS}/courses/id:{course_id}"
        headers = get_auth_header(API_KEY)
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 404:
            print(f"Course with course_id:{course_id} does not exist. Skipping.")
            return

        response.raise_for_status()
        course_data = response.json()

        # Get enrolled users from API
        enrolled_users = course_data.get('users', [])
        talentlms_user_ids = [user['id'] for user in enrolled_users if 'id' in user]
        
        # Pre-fetch user settings to avoid multiple DB queries
        user_settings = UserSettings.objects.filter(user_id_talentlms__in=talentlms_user_ids)
        valid_user_ids = set(user_settings.values_list('user_id_talentlms', flat=True))
        
        # Pre-fetch existing completed records to avoid processing them
        completed_records = set(ElearninStates.objects.filter(
            user__user_id_talentlms__in=talentlms_user_ids,
            course_id=int(course_id),
            self_learning_completion=100
        ).values_list('user__user_id_talentlms', flat=True))
        
        print(f"Found {len(completed_records)} already completed records for course {course_id}")

        for user_id in talentlms_user_ids:
            if user_id in valid_user_ids:
                if user_id in completed_records:
                    print(f"Skipping user_id={user_id} for course_id={course_id} (already completed).")
                    continue
                # Process each valid user
                fetch_user_course_status(course_id, user_id)
                # Add small delay to reduce database contention
                import time
                time.sleep(0.1)  # 100ms delay between users
            else:
                print(f"Skipping user_id={user_id} as it is not found in UserSettings")
                
    except requests.RequestException as exc:
        print(f"API request failed for course_id:{course_id}: {exc}")
    except Exception as exc:
        print(f"Unexpected error for course_id:{course_id}: {exc}")

@shared_task(bind=True, name="get_user_course_report_on_talent_lms")
def get_user_course_report_on_talent_lms(self):
    """
    Main Celery task to initiate the retrieval of course status from TalentLMS.
    Dispatches subtasks for each course to process enrolled users.
    """
    try:
        # Pre-fetch all courses with TalentLMS IDs
        courses = Course.objects.filter(course_id_talent_lms__isnull=False)
        course_count = courses.count()
        
        if course_count == 0:
            print("No courses with TalentLMS IDs found. Exiting.")
            return
            
        print(f"Found {course_count} courses with TalentLMS IDs")
        
        # Process each course
        for course in courses:
            course_id = course.course_id_talent_lms
            if not course_id or not str(course_id).isdigit():
                print(f"Invalid course_id: {course_id}. Skipping.")
                continue

            print(f"Processing course: {course.name} (ID: {course_id})")
            process_course_enrollments(course_id)

            # Add delay between courses to reduce database load
            import time
            time.sleep(0.5)  # 500ms delay between courses
            
    except Exception as exc:
        print(f"Task failed: {exc}")
        raise

# Get Lime Survey Report & Syncing into db
def get_lime_survey_assessment_report(survey_id):
    """
    Helper function to interact with LimeSurvey API, retrieve survey responses,
    and decode them from base64.

    Args:
        survey_id (int): The ID of the survey.

    Returns:
        str: The decoded survey responses.

    Raises:
        Exception: If authentication or response export fails.
    """
    # Initialize a session to maintain cookies
    session = requests.Session()
    session.headers.update({'Content-Type': 'application/json'})
    session.cookies.set('LS-KWKDYLLCEJFZSAOH', 'k1hkc4l9s4l8ukn1m03oec3on3')
    url = deviare_settings.LIME_SURVEY_URL

    # Step 1: Get session key
    auth_data = {
        "method": "get_session_key",
        "params": ["admin", "LimeSurvey@2025"],
        "id": 1
    }
    auth_response = session.post(url, json=auth_data)
    auth_response_data = auth_response.json()

    if 'result' not in auth_response_data or not auth_response_data['result']:
        raise Exception(f"Failed to get session key: {auth_response_data}")
    session_key = auth_response_data['result']

    # Step 2: Export survey responses
    export_data = {
        "method": "export_responses",
        "params": [session_key, survey_id, "json", "en", "all", "code", "short"],
        "id": 1
    }
    export_response = session.post(url, json=export_data)
    export_response_data = export_response.json()

    if 'result' not in export_response_data or not export_response_data['result']:
        raise Exception(f"Failed to export responses: {export_response_data}")
    encoded_responses = export_response_data['result']

    # Step 3: Decode the base64 response
    decoded_bytes = base64.b64decode(encoded_responses)
    decoded_str = decoded_bytes.decode('utf-8')
    data = json.loads(decoded_str)
    responses = data.get("responses", [])
    
    print(f"Retrieved {len(responses)} responses for survey ID {survey_id}")
    return responses

def save_survey_responses(response, survey_id):
    """
    Save survey responses from JSON data into the SurveyResponse model.

    Args:
        response (dict): Dictionary containing survey response data.
        survey_id (int): The ID of the survey.

    Raises:
        Exception: If the saving process fails.
    """
    try:
        # Parse and make submitdate aware
        submitdate_str = response.get("submitdate")
        submitdate = None
        if submitdate_str:
            try:
                submitdate = parser.parse(submitdate_str)
                if submitdate.tzinfo is None:
                    # If naive, assume UTC and make it aware
                    submitdate = timezone.make_aware(submitdate, timezone.utc)
            except ValueError:
                print(f"Invalid submitdate format: {submitdate_str}")
                submitdate = None

        # Use the processed submitdate in the defaults dictionary
        SurveyResponse.objects.update_or_create(
            id=response.get("id"),
            defaults={
                "survey_id": survey_id,
                "submitdate": submitdate,
                "lastpage": response.get("lastpage"),
                "startlanguage": response.get("startlanguage"),
                "seed": response.get("seed"),
                "token": response.get("token"),
                "G0Q1": response.get("G0Q1"),
                "G0Q2": response.get("G0Q2"),
                "G0Q3": response.get("G0Q3"),
                "G0Q4": response.get("G0Q4"),
                "G0Q5": response.get("G0Q5"),
                "G0Q6": response.get("G0Q6"),
                "G0Q7": response.get("G0Q7"),
                "G0Q8": response.get("G0Q8"),
                "G1Q1_SQ1": response.get("G1Q1[SQ1]"),
                "G1Q1_SQ2": response.get("G1Q1[SQ2]"),
                "G1Q2_SQ1": response.get("G1Q2[SQ1]"),
                "G1Q2_SQ2": response.get("G1Q2[SQ2]"),
                "G1Q3_SQ1": response.get("G1Q3[SQ1]"),
                "G1Q3_SQ2": response.get("G1Q3[SQ2]"),
                "G1Q4_SQ1": response.get("G1Q4[SQ1]"),
                "G1Q4_SQ2": response.get("G1Q4[SQ2]"),
                "G1Q5_SQ1": response.get("G1Q5[SQ1]"),
                "G1Q5_SQ2": response.get("G1Q5[SQ2]"),
                "G1Q6_SQ1": response.get("G1Q6[SQ1]"),
                "G1Q6_SQ2": response.get("G1Q6[SQ2]"),
                "G1Q7_SQ1": response.get("G1Q7[SQ1]"),
                "G1Q7_SQ2": response.get("G1Q7[SQ2]"),
                "G1Q8_SQ1": response.get("G1Q8[SQ1]"),
                "G1Q8_SQ2": response.get("G1Q8[SQ2]"),
                "G1Q9_SQ1": response.get("G1Q9[SQ1]"),
                "G1Q9_SQ2": response.get("G1Q9[SQ2]"),
                "G1Q10_SQ1": response.get("G1Q10[SQ1]"),
                "G1Q10_SQ2": response.get("G1Q10[SQ2]"),
                "G1Q11_SQ1": response.get("G1Q11[SQ1]"),
                "G1Q11_SQ2": response.get("G1Q11[SQ2]"),
                "G2Q1_SQ1": response.get("G2Q1[SQ1]"),
                "G2Q1_SQ2": response.get("G2Q1[SQ2]"),
                "G2Q2_SQ1": response.get("G2Q2[SQ1]"),
                "G2Q2_SQ2": response.get("G2Q2[SQ2]"),
                "G2Q3_SQ1": response.get("G2Q3[SQ1]"),
                "G2Q3_SQ2": response.get("G2Q3[SQ2]"),
                "G2Q4_SQ1": response.get("G2Q4[SQ1]"),
                "G2Q4_SQ2": response.get("G2Q4[SQ2]"),
                "G2Q5_SQ1": response.get("G2Q5[SQ1]"),
                "G2Q5_SQ2": response.get("G2Q5[SQ2]"),
                "G02Q6_SQ1": response.get("G02Q6[SQ1]"),
                "G02Q6_SQ2": response.get("G02Q6[SQ2]"),
                "G02Q7_SQ1": response.get("G02Q7[SQ1]"),
                "G02Q7_SQ2": response.get("G02Q7[SQ2]"),
                "G02Q8_SQ1": response.get("G02Q8[SQ1]"),
                "G02Q8_SQ2": response.get("G02Q8[SQ2]"),
                "G02Q9_SQ1": response.get("G02Q9[SQ1]"),
                "G02Q9_SQ2": response.get("G02Q9[SQ2]"),
                "G02Q10_SQ1": response.get("G02Q10[SQ1]"),
                "G02Q10_SQ2": response.get("G02Q10[SQ2]"),
                "G02Q11_SQ1": response.get("G02Q11[SQ1]"),
                "G02Q11_SQ2": response.get("G02Q11[SQ2]"),
                "G3Q1_SQ1": response.get("G3Q1[SQ1]"),
                "G3Q1_SQ2": response.get("G3Q1[SQ2]"),
                "G3Q2_SQ1": response.get("G3Q2[SQ1]"),
                "G3Q2_SQ2": response.get("G3Q2[SQ2]"),
                "G3Q3_SQ1": response.get("G3Q3[SQ1]"),
                "G3Q3_SQ2": response.get("G3Q3[SQ2]"),
                "G3Q4_SQ1": response.get("G3Q4[SQ1]"),
                "G3Q4_SQ2": response.get("G3Q4[SQ2]"),
                "G4Q1_SQ1": response.get("G4Q1[SQ1]"),
                "G4Q1_SQ2": response.get("G4Q1[SQ2]"),
                "G4Q2_SQ1": response.get("G4Q2[SQ1]"),
                "G4Q2_SQ2": response.get("G4Q2[SQ2]"),
                "G4Q3_SQ1": response.get("G4Q3[SQ1]"),
                "G4Q3_SQ2": response.get("G4Q3[SQ2]"),
                "G4Q4_SQ1": response.get("G4Q4[SQ1]"),
                "G4Q4_SQ2": response.get("G4Q4[SQ2]"),
                "G4Q5_SQ1": response.get("G4Q5[SQ1]"),
                "G4Q5_SQ2": response.get("G4Q5[SQ2]"),
                "G4Q6_SQ1": response.get("G4Q6[SQ1]"),
                "G4Q6_SQ2": response.get("G4Q6[SQ2]"),
                "G4Q7_SQ1": response.get("G4Q7[SQ1]"),
                "G4Q7_SQ2": response.get("G4Q7[SQ2]"),
                "G4Q8_SQ1": response.get("G4Q8[SQ1]"),
                "G4Q8_SQ2": response.get("G4Q8[SQ2]"),
                "G4Q9_SQ1": response.get("G4Q9[SQ1]"),
                "G4Q9_SQ2": response.get("G4Q9[SQ2]"),
                "G4Q10_SQ1": response.get("G4Q10[SQ1]"),
                "G4Q10_SQ2": response.get("G4Q10[SQ2]"),
                "G5Q1_SQ1": response.get("G5Q1[SQ1]"),
                "G5Q1_SQ2": response.get("G5Q1[SQ2]"),
                "G5Q2_SQ1": response.get("G5Q2[SQ1]"),
                "G5Q2_SQ2": response.get("G5Q2[SQ2]"),
                "G5Q3_SQ1": response.get("G5Q3[SQ1]"),
                "G5Q3_SQ2": response.get("G5Q3[SQ2]"),
                "G5Q4_SQ1": response.get("G5Q4[SQ1]"),
                "G5Q4_SQ2": response.get("G5Q4[SQ2]"),
                "G5Q5_SQ1": response.get("G5Q5[SQ1]"),
                "G5Q5_SQ2": response.get("G5Q5[SQ2]"),
                "G5Q6_SQ1": response.get("G5Q6[SQ1]"),
                "G5Q6_SQ2": response.get("G5Q6[SQ2]"),
                "G5Q7_SQ1": response.get("G5Q7[SQ1]"),
                "G5Q7_SQ2": response.get("G5Q7[SQ2]"),
                "G5Q8_SQ1": response.get("G5Q8[SQ1]"),
                "G5Q8_SQ2": response.get("G5Q8[SQ2]"),
                "G5Q9_SQ1": response.get("G5Q9[SQ1]"),
                "G5Q9_SQ2": response.get("G5Q9[SQ2]"),
                "G5Q10_SQ1": response.get("G5Q10[SQ1]"),
                "G5Q10_SQ2": response.get("G5Q10[SQ2]"),
                "G5Q11_SQ1": response.get("G5Q11[SQ1]"),
                "G5Q11_SQ2": response.get("G5Q11[SQ2]"),
                "G5Q12_SQ1": response.get("G5Q12[SQ1]"),
                "G5Q12_SQ2": response.get("G5Q12[SQ2]"),
                "G5Q13_SQ1": response.get("G5Q13[SQ1]"),
                "G5Q13_SQ2": response.get("G5Q13[SQ2]"),
                "G5Q14_SQ1": response.get("G5Q14[SQ1]"),
                "G5Q14_SQ2": response.get("G5Q14[SQ2]"),
                "G5Q15_SQ1": response.get("G5Q15[SQ1]"),
                "G5Q15_SQ2": response.get("G5Q15[SQ2]"),
                "G5Q16_SQ1": response.get("G5Q16[SQ1]"),
                "G5Q16_SQ2": response.get("G5Q16[SQ2]"),
                "G5Q17_SQ1": response.get("G5Q17[SQ1]"),
                "G5Q17_SQ2": response.get("G5Q17[SQ2]"),
                "G5Q18_SQ1": response.get("G5Q18[SQ1]"),
                "G5Q18_SQ2": response.get("G5Q18[SQ2]"),
                "G5Q19_SQ1": response.get("G5Q19[SQ1]"),
                "G5Q19_SQ2": response.get("G5Q19[SQ2]"),
                "G5Q20_SQ1": response.get("G5Q20[SQ1]"),
                "G5Q20_SQ2": response.get("G5Q20[SQ2]"),
                "G5Q21_SQ1": response.get("G5Q21[SQ1]"),
                "G5Q21_SQ2": response.get("G5Q21[SQ2]"),
                "G5Q22_SQ1": response.get("G5Q22[SQ1]"),
                "G5Q22_SQ2": response.get("G5Q22[SQ2]"),
                "G5Q23_SQ1": response.get("G5Q23[SQ1]"),
                "G5Q23_SQ2": response.get("G5Q23[SQ2]"),
                "G5Q24_SQ1": response.get("G5Q24[SQ1]"),
                "G5Q24_SQ2": response.get("G5Q24[SQ2]"),
                "G5Q25_SQ1": response.get("G5Q25[SQ1]"),
                "G5Q25_SQ2": response.get("G5Q25[SQ2]"),
                "G5Q26_SQ1": response.get("G5Q26[SQ1]"),
                "G5Q26_SQ2": response.get("G5Q26[SQ2]"),
                "G5Q27_SQ1": response.get("G5Q27[SQ1]"),
                "G5Q27_SQ2": response.get("G5Q27[SQ2]"),
                "G5Q28_SQ1": response.get("G5Q28[SQ1]"),
                "G5Q28_SQ2": response.get("G5Q28[SQ2]"),
                "G5Q29_SQ1": response.get("G5Q29[SQ1]"),
                "G5Q29_SQ2": response.get("G5Q29[SQ2]"),
                "G5Q30_SQ1": response.get("G5Q30[SQ1]"),
                "G5Q30_SQ2": response.get("G5Q30[SQ2]"),
                "G5Q31_SQ1": response.get("G5Q31[SQ1]"),
                "G5Q31_SQ2": response.get("G5Q31[SQ2]"),
                "G5Q32_SQ1": response.get("G5Q32[SQ1]"),
                "G5Q32_SQ2": response.get("G5Q32[SQ2]"),
                "G5Q33_SQ1": response.get("G5Q33[SQ1]"),
                "G5Q33_SQ2": response.get("G5Q33[SQ2]"),
                "G6Q1_SQ1": response.get("G6Q1[SQ1]"),
                "G6Q1_SQ2": response.get("G6Q1[SQ2]"),
                "G6Q2_SQ1": response.get("G6Q2[SQ1]"),
                "G6Q2_SQ2": response.get("G6Q2[SQ2]"),
                "G6Q3_SQ1": response.get("G6Q3[SQ1]"),
                "G6Q3_SQ2": response.get("G6Q3[SQ2]"),
                "G6Q4_SQ1": response.get("G6Q4[SQ1]"),
                "G6Q4_SQ2": response.get("G6Q4[SQ2]"),
                "G6Q5_SQ1": response.get("G6Q5[SQ1]"),
                "G6Q5_SQ2": response.get("G6Q5[SQ2]"),
                "G6Q6_SQ1": response.get("G6Q6[SQ1]"),
                "G6Q6_SQ2": response.get("G6Q6[SQ2]"),
                "G6Q7_SQ1": response.get("G6Q7[SQ1]"),
                "G6Q7_SQ2": response.get("G6Q7[SQ2]"),
                "G6Q8_SQ1": response.get("G6Q8[SQ1]"),
                "G6Q8_SQ2": response.get("G6Q8[SQ2]"),
                "G6Q9_SQ1": response.get("G6Q9[SQ1]"),
                "G6Q9_SQ2": response.get("G6Q9[SQ2]"),
                "G6Q10_SQ1": response.get("G6Q10[SQ1]"),
                "G6Q10_SQ2": response.get("G6Q10[SQ2]"),
                "G6Q11_SQ1": response.get("G6Q11[SQ1]"),
                "G6Q11_SQ2": response.get("G6Q11[SQ2]"),
                "G6Q12_SQ1": response.get("G6Q12[SQ1]"),
                "G6Q12_SQ2": response.get("G6Q12[SQ2]"),
                "G7Q1": response.get("G7Q1"),
                "G7Q2": response.get("G7Q2"),
                "G7Q2_filecount": response.get("G7Q2[filecount]"),
            }
        )
        print(f"Saved response ID {response.get('id')} for survey ID {survey_id}")

    except Exception as e:
        print(f"Failed to save survey responses for survey ID {survey_id}: {e}")
        raise

@shared_task(bind=True, name="get_lime_survey_responses")
def get_lime_survey_responses(self):
    """
    Orchestrate retrieval and saving of survey responses one by one.
    
    Args:
        self: The task instance, provided by Celery when bind=True.
    """
    try:
        survey_id = deviare_settings.DTMM_SURVEY_ID
        if not survey_id:
            raise ValueError("DTMM_SURVEY_ID is not set in the .env file")
        
        survey_id = int(survey_id)
        responses = get_lime_survey_assessment_report(survey_id)
        
        # Process responses one by one
        for response in responses:
            save_survey_responses(response, survey_id)
        
        print(f"Dispatched save tasks for {len(responses)} responses")
    except Exception as e:
        print(f"Task failed: {e}")
        raise

# Get Lime Survey Report & Syncing into db
def get_AI_lime_survey_assessment_report(survey_id):
    """
    Helper function to interact with LimeSurvey API, retrieve survey responses,
    and decode them from base64.

    Args:
        survey_id (int): The ID of the survey.

    Returns:
        str: The decoded survey responses.

    Raises:
        Exception: If authentication or response export fails.
    """
    # Initialize a session to maintain cookies
    session = requests.Session()
    session.headers.update({'Content-Type': 'application/json'})
    session.cookies.set('LS-KWKDYLLCEJFZSAOH', 'k1hkc4l9s4l8ukn1m03oec3on3')
    url = deviare_settings.LIME_SURVEY_URL

    # Step 1: Get session key
    auth_data = {
        "method": "get_session_key",
        "params": ["admin", "LimeSurvey@2025"],
        "id": 1
    }
    auth_response = session.post(url, json=auth_data)
    auth_response_data = auth_response.json()

    if 'result' not in auth_response_data or not auth_response_data['result']:
        raise Exception(f"Failed to get session key: {auth_response_data}")
    session_key = auth_response_data['result']

    # Step 2: Export survey responses
    export_data = {
        "method": "export_responses",
        "params": [session_key, survey_id, "json", "en", "all", "code", "short"],
        "id": 1
    }
    export_response = session.post(url, json=export_data)
    export_response_data = export_response.json()

    if 'result' not in export_response_data or not export_response_data['result']:
        raise Exception(f"Failed to export responses: {export_response_data}")
    encoded_responses = export_response_data['result']

    # Step 3: Decode the base64 response
    decoded_bytes = base64.b64decode(encoded_responses)
    decoded_str = decoded_bytes.decode('utf-8')
    data = json.loads(decoded_str)
    responses = data.get("responses", [])
    
    print(f"Retrieved {len(responses)} responses for survey ID {survey_id}")
    return responses

def save_AI_survey_responses(response, survey_id):
    """
    Save survey responses from JSON data into the AISurveyResponse model.

    Args:
        response (dict): Dictionary containing survey response data.
        survey_id (int): The ID of the survey.

    Raises:
        Exception: If the saving process fails.
    """
    try:
        # Helper function to parse and make dates timezone-aware
        def parse_date(date_str):
            if date_str:
                try:
                    date = parser.parse(date_str)
                    if date.tzinfo is None:
                        date = timezone.make_aware(date, timezone.utc)
                    return date
                except ValueError:
                    print(f"Invalid date format: {date_str}")
            return None

        # Parse all date fields
        submitdate = parse_date(response.get("submitdate"))
        startdate = parse_date(response.get("startdate"))
        datestamp = parse_date(response.get("datestamp"))

        # Save or update the survey response
        AISurveyResponse.objects.update_or_create(
            id=response.get("id"),
            defaults={
                "survey_id": survey_id,
                "submitdate": submitdate,
                "lastpage": response.get("lastpage"),
                "startlanguage": response.get("startlanguage"),
                "seed": response.get("seed"),
                "token": response.get("token"),
                "startdate": startdate,
                "datestamp": datestamp,
                "G0Q1": response.get("G0Q1"),
                "G0Q2": response.get("G0Q2"),
                "G0Q3": response.get("G0Q3"),
                "G0Q4": response.get("G0Q4"),
                "G0Q5": response.get("G0Q5"),
                "G0Q6": response.get("G0Q6"),
                "G0Q7": response.get("G0Q7"),
                "G0Q8": response.get("G0Q8"),
                "G1Q1_SQ1": response.get("G1Q1[SQ1]"),
                "G1Q1_SQ2": response.get("G1Q1[SQ2]"),
                "G1Q2_SQ1": response.get("G1Q2[SQ1]"),
                "G1Q2_SQ2": response.get("G1Q2[SQ2]"),
                "G1Q3_SQ1": response.get("G1Q3[SQ1]"),
                "G1Q3_SQ2": response.get("G1Q3[SQ2]"),
                "G1Q4_SQ1": response.get("G1Q4[SQ1]"),
                "G1Q4_SQ2": response.get("G1Q4[SQ2]"),
                "G1Q5_SQ1": response.get("G1Q5[SQ1]"),
                "G1Q5_SQ2": response.get("G1Q5[SQ2]"),
                "G1Q6_SQ1": response.get("G1Q6[SQ1]"),
                "G1Q6_SQ2": response.get("G1Q6[SQ2]"),
                "G1Q7_SQ1": response.get("G1Q7[SQ1]"),
                "G1Q7_SQ2": response.get("G1Q7[SQ2]"),
                "G1Q8_SQ1": response.get("G1Q8[SQ1]"),
                "G1Q8_SQ2": response.get("G1Q8[SQ2]"),
                "G1Q9_SQ1": response.get("G1Q9[SQ1]"),
                "G1Q9_SQ2": response.get("G1Q9[SQ2]"),
                "G2Q1_SQ1": response.get("G2Q1[SQ1]"),
                "G2Q1_SQ2": response.get("G2Q1[SQ2]"),
                "G2Q2_SQ1": response.get("G2Q2[SQ1]"),
                "G2Q2_SQ2": response.get("G2Q2[SQ2]"),
                "G2Q3_SQ1": response.get("G2Q3[SQ1]"),
                "G2Q3_SQ2": response.get("G2Q3[SQ2]"),
                "G2Q4_SQ1": response.get("G2Q4[SQ1]"),
                "G2Q4_SQ2": response.get("G2Q4[SQ2]"),
                "G2Q5_SQ1": response.get("G2Q5[SQ1]"),
                "G2Q5_SQ2": response.get("G2Q5[SQ2]"),
                "G02Q6_SQ1": response.get("G02Q6[SQ1]"),
                "G02Q6_SQ2": response.get("G02Q6[SQ2]"),
                "G02Q7_SQ1": response.get("G02Q7[SQ1]"),
                "G02Q7_SQ2": response.get("G02Q7[SQ2]"),
                "G02Q8_SQ1": response.get("G02Q8[SQ1]"),
                "G02Q8_SQ2": response.get("G02Q8[SQ2]"),
                "G02Q9_SQ1": response.get("G02Q9[SQ1]"),
                "G02Q9_SQ2": response.get("G02Q9[SQ2]"),
                "G3Q1_SQ1": response.get("G3Q1[SQ1]"),
                "G3Q1_SQ2": response.get("G3Q1[SQ2]"),
                "G3Q2_SQ1": response.get("G3Q2[SQ1]"),
                "G3Q2_SQ2": response.get("G3Q2[SQ2]"),
                "G3Q3_SQ1": response.get("G3Q3[SQ1]"),
                "G3Q3_SQ2": response.get("G3Q3[SQ2]"),
                "G3Q4_SQ1": response.get("G3Q4[SQ1]"),
                "G3Q4_SQ2": response.get("G3Q4[SQ2]"),
                "G3Q5_SQ1": response.get("G3Q5[SQ1]"),
                "G3Q5_SQ2": response.get("G3Q5[SQ2]"),
                "G3Q6_SQ1": response.get("G3Q6[SQ1]"),
                "G3Q6_SQ2": response.get("G3Q6[SQ2]"),
                "G3Q7_SQ1": response.get("G3Q7[SQ1]"),
                "G3Q7_SQ2": response.get("G3Q7[SQ2]"),
                "G3Q8_SQ1": response.get("G3Q8[SQ1]"),
                "G3Q8_SQ2": response.get("G3Q8[SQ2]"),
                "G3Q9_SQ1": response.get("G3Q9[SQ1]"),
                "G3Q9_SQ2": response.get("G3Q9[SQ2]"),
                "G4Q1_SQ1": response.get("G4Q1[SQ1]"),
                "G4Q1_SQ2": response.get("G4Q1[SQ2]"),
                "G4Q2_SQ1": response.get("G4Q2[SQ1]"),
                "G4Q2_SQ2": response.get("G4Q2[SQ2]"),
                "G4Q3_SQ1": response.get("G4Q3[SQ1]"),
                "G4Q3_SQ2": response.get("G4Q3[SQ2]"),
                "G4Q4_SQ1": response.get("G4Q4[SQ1]"),
                "G4Q4_SQ2": response.get("G4Q4[SQ2]"),
                "G4Q5_SQ1": response.get("G4Q5[SQ1]"),
                "G4Q5_SQ2": response.get("G4Q5[SQ2]"),
                "G4Q6_SQ1": response.get("G4Q6[SQ1]"),
                "G4Q6_SQ2": response.get("G4Q6[SQ2]"),
                "G4Q7_SQ1": response.get("G4Q7[SQ1]"),
                "G4Q7_SQ2": response.get("G4Q7[SQ2]"),
                "G4Q8_SQ1": response.get("G4Q8[SQ1]"),
                "G4Q8_SQ2": response.get("G4Q8[SQ2]"),
                "G4Q9_SQ1": response.get("G4Q9[SQ1]"),
                "G4Q9_SQ2": response.get("G4Q9[SQ2]"),
                "G5Q1_SQ1": response.get("G5Q1[SQ1]"),
                "G5Q1_SQ2": response.get("G5Q1[SQ2]"),
                "G5Q2_SQ1": response.get("G5Q2[SQ1]"),
                "G5Q2_SQ2": response.get("G5Q2[SQ2]"),
                "G5Q3_SQ1": response.get("G5Q3[SQ1]"),
                "G5Q3_SQ2": response.get("G5Q3[SQ2]"),
                "G5Q4_SQ1": response.get("G5Q4[SQ1]"),
                "G5Q4_SQ2": response.get("G5Q4[SQ2]"),
                "G5Q5_SQ1": response.get("G5Q5[SQ1]"),
                "G5Q5_SQ2": response.get("G5Q5[SQ2]"),
                "G5Q6_SQ1": response.get("G5Q6[SQ1]"),
                "G5Q6_SQ2": response.get("G5Q6[SQ2]"),
                "G5Q7_SQ1": response.get("G5Q7[SQ1]"),
                "G5Q7_SQ2": response.get("G5Q7[SQ2]"),
                "G5Q8_SQ1": response.get("G5Q8[SQ1]"),
                "G5Q8_SQ2": response.get("G5Q8[SQ2]"),
                "G5Q9_SQ1": response.get("G5Q9[SQ1]"),
                "G5Q9_SQ2": response.get("G5Q9[SQ2]"),
                "G6Q1_SQ1": response.get("G6Q1[SQ1]"),
                "G6Q1_SQ2": response.get("G6Q1[SQ2]"),
                "G6Q2_SQ1": response.get("G6Q2[SQ1]"),
                "G6Q2_SQ2": response.get("G6Q2[SQ2]"),
                "G6Q3_SQ1": response.get("G6Q3[SQ1]"),
                "G6Q3_SQ2": response.get("G6Q3[SQ2]"),
                "G6Q4_SQ1": response.get("G6Q4[SQ1]"),
                "G6Q4_SQ2": response.get("G6Q4[SQ2]"),
                "G6Q5_SQ1": response.get("G6Q5[SQ1]"),
                "G6Q5_SQ2": response.get("G6Q5[SQ2]"),
                "G6Q6_SQ1": response.get("G6Q6[SQ1]"),
                "G6Q6_SQ2": response.get("G6Q6[SQ2]"),
                "G6Q7_SQ1": response.get("G6Q7[SQ1]"),
                "G6Q7_SQ2": response.get("G6Q7[SQ2]"),
                "G6Q8_SQ1": response.get("G6Q8[SQ1]"),
                "G6Q8_SQ2": response.get("G6Q8[SQ2]"),
                "G6Q9_SQ1": response.get("G6Q9[SQ1]"),
                "G6Q9_SQ2": response.get("G6Q9[SQ2]"),
                "G7Q1": response.get("G7Q1"),
                "G7Q1_filecount": response.get("G7Q1[filecount]"),
            }
        )
        print(f"Saved response ID {response.get('id')} for survey ID {survey_id}")

    except Exception as e:
        print(f"Failed to save survey responses for survey ID {survey_id}: {e}")
        raise

@shared_task(bind=True, name="get_AI_lime_survey_responses")
def get_AI_lime_survey_responses(self):
    """
    Orchestrate retrieval and saving of survey responses one by one.
    
    Args:
        self: The task instance, provided by Celery when bind=True.
    """
    try:
        survey_id = deviare_settings.AIMM_SURVEY_ID
        if not survey_id:
            raise ValueError("AIMM_SURVEY_ID is not set in the .env file")
        
        survey_id = int(survey_id)
        responses = get_AI_lime_survey_assessment_report(survey_id)
        
        # Process responses one by one
        for response in responses:
            save_AI_survey_responses(response, survey_id)
        
        print(f"Dispatched save tasks for {len(responses)} responses")
    except Exception as e:
        print(f"Task failed: {e}")
        raise

@shared_task(bind=True, name="create_gcindex_token")
def create_gcindex_token(self, *args, **kwargs):
    import time
    try:
        url = f"{GCINDEX_API_URL}?key={GCINDEX_API_KEY}"
        t = time.time()
        assessment = GCIndexAssessment.objects.get(pk=kwargs.get('uuid'))
        payload = {
            "action": "create",
            "first_name": assessment.user.firstName,
            "last_name": assessment.user.lastName,
            "email": assessment.user.email
        }
        headers = {
            'Content-Type': 'application/json'
        }
        n = time.time()
        print(n - t)
        print(url, headers, payload)

        response = requests.post(url, headers=headers, data=json.dumps(payload))
        t = time.time()
        print(t - n)
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False) is not False:
                if assessment.token is None:
                    assessment.token = result.get('token')
                    assessment.save()
            n = time.time()
            print(n - t)
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="report_gcindex")
def report_gcindex(self, *args, **kwargs):
    from base64 import b64decode
    try:
        gc = GCIndexAssessment.objects.get(uuid=kwargs.get('uuid'))
        resp = gcapi(action='report', token=gc.token)
        if resp.ok:
            r = resp.json()

            if r and r.get('success', False):
                gcreport = GCIndexReport.objects.create(
                    assessment=gc,
                    report=b64decode(r['data']),
                    report_data=dict(base64=r['data'], type=r['type']))
                gc.state_id = 9
                gc.save()
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="delete_gcindex_token")
def delete_gcindex_token(self, *args, **kwargs):
    try:
        resp = gcapi(action='delete', token=kwargs.get('token'))
        if resp.ok:
            r = resp.json()
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="fetch_gcindex_single")
def fetch_gcindex_single(self, *args, **kwargs):
    try:
        logger.info('fetch_gcindex_single')
        url = f"{GCINDEX_API_URL}?key={GCINDEX_API_KEY}"
        assessment = GCIndexAssessment.objects.get(pk=kwargs.get('uuid'))
        payload = {
            "action": "fetch",
            "token": assessment.token,
        }
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            result = response.json()

            if result.get('success', False) is not False:
                if assessment.token is None:
                    create_gcindex_token.apply_async(kwargs={'uuid': assessment.pk})
                else:
                    token_data = result.get('token')
                    for k in [
                        "completed",
                        "completed_at",
                        "has_report",
                        "url"
                    ]:
                        v = token_data.get(k)
                        if k == "completed_at" and v:
                            v = datetime.datetime.strptime(v, "%Y-%m-%d %H:%M")
                        setattr(assessment, k, v)
                    # if assessment.completed and assessment.report.count() == 0:
                    #     report_gcindex.apply_async(kwargs={'uuid': assessment.pk})

                    assessment.save()
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="fetch_gcindex_report")
def fetch_gcindex_report(self, *args, **kwargs):
    try:
        logger.info('fetch_gcindex_report')
        url = f"{GCINDEX_API_URL}?key={GCINDEX_API_KEY}"
        assessment = GCIndexAssessment.objects.get(pk=kwargs.get('uuid'))
        payload = {
            "action": "report",
            "token": assessment.token,
        }
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            result = response.json()

            if result.get('success', False) is not False:
                if assessment.token is None:
                    create_gcindex_token.apply_async(kwargs={'uuid': assessment.pk})
                else:
                    token_data = result.get('token')
                    for k in [
                        "completed",
                        "completed_at",
                        "has_report",
                        "url"
                    ]:
                        v = token_data.get(k)
                        if k == "completed_at" and v:
                            v = datetime.datetime.strptime(v, "%Y-%m-%d %H:%M")
                        setattr(assessment, k, v)
                    assessment.save()
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="fetch_all_gcindex")
def fetch_all_gcindex(self, *args, **kwargs):
    try:
        url = f"{GCINDEX_API_URL}?key={GCINDEX_API_KEY}"

        payload = {
            "action": "fetch"
        }
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers, data=json.dumps(payload))
        # return response
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False) is not False:

                for token in result.get('tokens'):
                    data = {}
                    for k in [
                        "completed",
                        "completed_at",
                        "has_report",
                        "url"
                    ]:
                        v = token.get(k)
                        if k == "completed_at" and v:
                            v = datetime.datetime.strptime(v, "%Y-%m-%d %H:%M")
                        data[k] = v
                    GCIndexAssessment.objects.update_or_create(token=token.get('token'), defaults=data)
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="create_all_gcindex_tokens")
def create_all_gcindex_tokens(self, *args, **kwargs):
    try:
        logger.info('create_all_gcindex_tokens')
        qs = GCIndexAssessment.objects.filter(
            ~Q(gcologist__isnull=True) & Q(token__isnull=True), deleted=False,
        ).values('uuid')
        for uid in qs:
            create_gcindex_token.apply_async(kwargs=uid)
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="fetch_all_gcindex_detail")
def fetch_all_gcindex_detail(self, *args, **kwargs):
    try:
        logger.info('fetch_all_gcindex_detail')
        qs = GCIndexAssessment.objects.filter(
            Q(gcologist__isnull=False) & Q(token__isnull=False) & (
                    Q(has_report=False) | Q(completed=False)
            ), deleted=False,
        ).values('uuid')
        for uid in qs:
            fetch_gcindex_single.apply_async(kwargs=uid)
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="fetch_all_gcindex_url")
def fetch_all_gcindex_url(self, *args, **kwargs):
    try:
        logger.info('fetch_all_gcindex_url')
        cqs = GCIndexAssessment.objects.filter(
            gcologist__isnull=False,
            token__isnull=True,
            deleted=False
        ).values('uuid')
        for uid in cqs:
            create_gcindex_token.apply_async(kwargs=uid)

        qs = GCIndexAssessment.objects.filter(
            gcologist__isnull=False,
            token__isnull=False,
            deleted=False,
            url__isnull=True
        ).values('uuid')
        for uid in qs:
            fetch_gcindex_single.apply_async(kwargs=uid)
        logger.info('report_gcindex')
        rqs = GCIndexAssessment.objects.filter(
            report__report__isnull=True,
            has_report=True,
            deleted=False,
            completed_at__lt=timezone.now() - timedelta(minutes=80)
        ).values('uuid')
        for uid in rqs:
            report_gcindex.apply_async(kwargs=uid)
    except Exception as e:
        logger.exception(e)


@shared_task(bind=True, name="run_email_queue")
def run_email_queue(self, ):
    from notification.models import EmailMessage
    from tools.email import send_wrapped_email
    from time import time
    from django.utils import timezone
    logger.info('run_email_queue')
    print('run_email_queue')
    qs = EmailMessage.objects.filter(
        sent=False,
        proc_id__isnull=True,
        # send_after__lte=timezone.now()
    )
    qs.update(proc_id=round(time()))

    for em in qs:
        try:
            em.send()
        except Exception as exc:
            logger.exception(exc)


def del_lms_user(email):
    from main.fixer import delete_keycloak_user
    url = "%s/users/email:%s" % (URL_ADDRESS, str(email))
    resp = requests.request("GET", url, auth=(API_KEY, ""), )
    print(resp.json())
    ret = {'del_lms': False, 'del_kc': False}
    if resp.status_code == 200:
        user_id = resp.json().get('id')
        delete_url = "%s/deleteuser" % URL_ADDRESS
        d = dict(user_id=str(user_id), permanent='yes')
        delresp = requests.request("POST", delete_url, auth=(API_KEY, ""), data=d)
        if delresp.status_code == 200:
            ret['del_lms'] = delresp.json()

    ret['del_kc'] = delete_keycloak_user(email=email)

    return ret


def set_lms_email(self, *args, **kwargs):
    from main.fixer import update_keycloak_user
    import traceback
    import time
    """
    Update user profile lastname to email on talent lms
    """
    try:

        lurl = "%s/edituser" % (URL_ADDRESS)
        old_email = kwargs.get("old_email", None)
        u = UserSettings.objects.get(email=old_email)
        user_id = u.user_id_talentlms
        email = kwargs.get("email", False)
        ren_u = UserSettings.objects.get(email=email)
        ren_auth = ren_u.user
        f_email = f'old_{email}'
        ren_u.userName = f_email
        ren_u.email = f_email
        ren_auth.username = f_email
        ren_auth.email = f_email
        done = {}
        if old_email:
            done = del_lms_user(email)
            # if not done:
            #     return
        if user_id and email:
            ren_auth.save()
            ren_u.save()
            payload = {
                "user_id": user_id,
                "login": email,
                "email": email,
                # 'last_name': email,
                # 'first_name': ','
            }
            time.sleep(2.0)
            response = requests.request("POST", lurl, auth=(API_KEY, ""), headers={}, data=payload, files=[])

            if response.status_code == 200:
                done['lms_upd'] = response.json()
                done['kc_upd'] = update_keycloak_user(old_email=old_email, email=email)
                logger.info("email changed")
            else:
                logger.critical(response.text)
                done['lms_upd_error'] = str(response.text)
            auth_user = u.user
            u.userName = email
            u.email = email
            auth_user.username = email
            auth_user.email = email
            u.save()
            auth_user.save()
            return done
    except Exception as exc:
        traceback.print_stack()
        done['error'] = str(exc)
        logger.exception(exc)
    return done


@shared_task(bind=True, name="check-orders")
def check_orders(self):
    from wp_api.wcapi import WcApi
    from wp_api.views import process_order
    try:
        api = WcApi()
        orders = api.orders.read(params={'per_page': '1000'})
        for o in orders:
            process_order(o)
    except Exception as e:
        logger.exception(e)
        print(e)


# @app.on_after_configure.connect
# def setup_periodic_tasks(sender, **kwargs):
#    print('setup_periodic_tasks')
#    print(sender)
#    logger.info('setup_periodic_tasks')
#    logger.info(str(sender))
#    sender.add_periodic_task(
#       crontab(hour='*', minute=0),
#        fetch_all_gcindex_detail.s(),
#    )
#    sender.add_periodic_task(
#        crontab(minute='*'),
#        run_email_queue.s(),
#    )


@shared_task(bind=True, name="fetch_all_sparrow_response")
def fetch_all_sparrow_response(self, ):
    from main.views import SurveySparrow
    response = SurveySparrow()
    logger.info('fetch_all_sparrow_response', response)

@shared_task(bind=True,name="get_all_user_reports_from_xpert_skills")
def get_all_user_reports_from_xpert_skills(self, *args, **kwargs):
    """
    Periodic task. Retrieve all course content from
    talent lms platform
    """
    try:
        url = "https://www.learning.deviareacademy.africa/usersactivity/47"
        payload = {}
        headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
        response = requests.get(url, headers=headers, data=payload)
        print(f"get_all_user_reports_from_xpert_skills resp_code - {response.status_code}")
        if response.status_code == 200:
            content = json.loads(response.text)
            if content:
                for report in content:
                    if report['details'][0] == 'Not enrolled in any course yet':
                        continue
                    else:
                        for detail in report['details']:
                            user = UserSettings.objects.filter(email=report['user_email']).first()
                            data = {"email":report['user_email'],"course_title":detail['title'],"last_login_date":report["last_login"],"account_status":"Registered","self_learning_completion":float(detail["progress"][:-1]),"user":user if user else None,"name":user.firstName if user else None,"order_type":"Admin Assigned","certification_status":"Certified" if float(detail["progress"][:-1]) > 98.0 else "Not Certified","is_xpertskill_course":True}
                            obj, created = ElearninStates.objects.update_or_create(email=report['user_email'],course_title=detail['title'],defaults=data)
                            print(f"New Entry Created ---- {created}")
                            # if data.get("certification_status") == "Certified" and not obj.is_email_sent:
                            #     body = (
                            #         "<p>Dear Deviare Graduate,</p>"
                            #         f"<p>Congratulations on successfully completing your {detail['title']} with us. "
                            #         "We hope your experience with Deviare has accelerated your ambitions towards a successful and impactful digital career.</p>"
                            #         "<p>We are delighted to invite you to be part of our Talent Platform as the next phase of your career journey. "
                            #         "At Deviare, we work hard to connect our graduates to real work opportunities through our network of clients and partners. "
                            #         "The Talent Platform is an efficient way for you to find work that aligns with your goals. Moreover, it is an efficient way for potential employers to find you.</p>"
                            #         "<p>Find out more about the Talent Platform here: "
                            #         "<a href='https://newsletter.deviare.africa/ambition-meets-opportunity-deviare-talent-platform?hs_preview=DYvUWKPM-157620518433'>https://newsletter.deviare.africa/ambition-meets-opportunity-deviare-talent-platform?hs_preview=DYvUWKPM-157620518433</a></p>"
                            #         "<p>We already have the bulk of your information, and by clicking on this link "
                            #         "<a href='https://talent.deviare.africa'>https://talent.deviare.africa</a>, start the journey towards your dream job.</p>"
                            #         "<p>We hope to see you on the Talent Platform and look forward to being part of your career journey.</p>"
                            #         "<p>Best wishes,</p>"
                            #         "<p>Deviare Talent Team</p>"
                            #     )
                            #     subject = f"Congratulations on Completing {detail['title']}!"
                            #     try:
                            #         FROM = "<EMAIL>"
                            #         TO = report['user_email'] if type(report['user_email']) is list else [report['user_email']]
                            #         msg = send_mail(subject, body, FROM, TO, html_message=body, fail_silently=False)
                            #         obj.is_email_sent = True
                            #         obj.save()
                            #     except Exception as e:
                            #         print("Error while sending an email:",e)
                            #         logger.exception(e)
                            #         pass
                            if created:
                                print("New user report created with below details:")
                                print("Course Report : ", report)

    except Exception as exc:
        print(exc)
        logger.exception(exc)

# # adding the celery task for testing
# @shared_task(bind=True, name="celery_task_1")
# def celery_task_1(self, *args, **kwargs):
#     logs_li = []
#     try:
#         # Your task logic here
#         logs_li.append("Task 1 started")
#         print("Task 1 started")
#         # Simulate task processing
#         time.sleep(2)
#         logs_li.append("Task 1 processing")
#         print("Task 1 processing")
#         TaskResult.objects.filter(task_id=self.request.id).update(
#             result=logs_li
#         )
#         time.sleep(10)
#         logs_li.append("slept for 10 seconds")
#         print("slept for 10 seconds")
#         TaskResult.objects.filter(task_id=self.request.id).update(
#             result=logs_li
#         )
#         # Simulate task completion
#         logs_li.append("Task 1 completed")
#         result = "Task 1 completed successfully"
#         status = 'SUCCESS'
#         print(f"Task 1 completed successfully - {logs_li}")
#     except Exception as e:
#         logs_li.append(f"Task 1 failed: {str(e)}")
#         result = f"Task 1 failed: {str(e)}"
#         status = 'FAILURE'
#         traceback = self.request.traceback
#         TaskResult.objects.filter(task_id=self.request.id).update(
#             result=result,
#             traceback=traceback,
#             status=status
#         )
#         print(f"Task 1 failed: {str(e)}")
#         raise
#     finally:
#         # Update logs to the database
#         print(f"Task 1 finally block executed for id - {self.request.id} and logs - {logs_li}")
#         try:
#             print(f"Attempting to update TaskResult in finally block ---- {logs_li}")
#             update_count = TaskResult.objects.filter(task_id=self.request.id).update(
#                 result=logs_li,
#                 status=status,
#                 task_name = 'task_1'
#             )
            
#         except Exception as e:
#             print(f"Failed to update TaskResult: {str(e)}")
#         print("Task 1 finally block completed")


# Manual Triggered tasks
@shared_task(bind=True, name="cleanup_old_task_results")
def cleanup_old_task_results(self, days=1):
    """
    Periodic cleanup task to remove old Celery task results and their corresponding
    TriggeredTask records from the database.
    """
    cutoff = timezone.now() - timedelta(days=days)
    # Find old task_ids from TaskResult
    old_task_ids = list(TaskResult.objects.filter(date_done__lt=cutoff).values_list('task_id', flat=True))
    if old_task_ids:
        # Delete from TriggeredTask first
        TriggeredTask.objects.filter(task_id__in=old_task_ids).delete()
        # Then delete from TaskResult
        TaskResult.objects.filter(task_id__in=old_task_ids).delete()
        print(f"Deleted old task results and triggered tasks older than {days} days.")
    else:
        print(f"No old tasks to delete older than {days} days.")

