
 WARNING 2025-07-09 15:57:01,283 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-09 15:57:21,869 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-09 15:57:23,155 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-09 15:58:59,481 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 

