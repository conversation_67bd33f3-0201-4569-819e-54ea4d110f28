[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = *****************:deviare/api-backend.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "staging"]
	remote = origin
	merge = refs/heads/staging
	vscode-merge-base = origin/staging
[branch "updatetaskname"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/updatetaskname
[branch "savesurveyresponsedb"]
	vscode-merge-base = origin/updatetaskname
	remote = origin
	merge = refs/heads/savesurveyresponsedb
[branch "updattaskfordbupdate"]
	vscode-merge-base = origin/savesurveyresponsedb
	remote = origin
	merge = refs/heads/updattaskfordbupdate
[branch "updateCustomerAdminDashboard"]
	vscode-merge-base = origin/updattaskfordbupdate
	remote = origin
	merge = refs/heads/updateCustomerAdminDashboard
[branch "createApiforlimesurvey"]
	vscode-merge-base = origin/updateCustomerAdminDashboard
	remote = origin
	merge = refs/heads/createApiforlimesurvey
[branch "aiassesment"]
	vscode-merge-base = origin/createApiforlimesurvey
	remote = origin
	merge = refs/heads/aiassesment
[branch "csmadmincount"]
	vscode-merge-base = origin/aiassesment
	remote = origin
	merge = refs/heads/csmadmincount
[branch "assesmentname"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/assesmentname
[branch "fixassessment"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/fixassessment
[branch "talentaccess"]
	vscode-merge-base = origin/fixassessment
	remote = origin
	merge = refs/heads/talentaccess
[branch "revinovaupdatecode"]
	vscode-merge-base = origin/talentaccess
	remote = origin
	merge = refs/heads/revinovaupdatecode
[branch "Revinovaupdatecode"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/Revinovaupdatecode
[branch "aiassesmentreport"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/aiassesmentreport
[branch "updatetalentcode"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/updatetalentcode
[branch "celerytime"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/celerytime
[branch "badgefix"]
	vscode-merge-base = origin/staging
	remote = origin
	merge = refs/heads/badgefix
[branch "manualCeleryTrigger"]
	vscode-merge-base = origin/badgefix
	remote = origin
	merge = refs/heads/manualCeleryTrigger
[branch "asAssessmentResponse"]
	vscode-merge-base = origin/manualCeleryTrigger
	remote = origin
	merge = refs/heads/asAssessmentResponse
[branch "composer"]
	vscode-merge-base = origin/asAssessmentResponse
	remote = origin
	merge = refs/heads/composer
